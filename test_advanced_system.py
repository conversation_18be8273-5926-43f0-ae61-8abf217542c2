#!/usr/bin/env python3
"""
Advanced Trading System Test Suite
Comprehensive testing for all advanced components
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)

def setup_test_logging():
    """Set up logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def print_test_header(test_name):
    """Print a formatted test header"""
    print(f"\n{Fore.CYAN}🧪 {test_name}{Style.RESET_ALL}")
    print("=" * 70)

def print_test_result(test_name, success, details=""):
    """Print test result"""
    if success:
        print(f"{Fore.GREEN}✅ {test_name}: PASSED{Style.RESET_ALL}")
        if details:
            print(f"   {details}")
    else:
        print(f"{Fore.RED}❌ {test_name}: FAILED{Style.RESET_ALL}")
        if details:
            print(f"   {details}")

def test_environment_setup():
    """Test 1: Environment and Dependencies"""
    print_test_header("Environment and Dependencies Test")
    
    try:
        # Load environment variables
        load_dotenv('config.env')
        
        # Check required environment variables
        required_vars = [
            'OPENAI_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print_test_result("Environment Variables", False, f"Missing: {', '.join(missing_vars)}")
            return False
        
        print_test_result("Environment Variables", True, "All required variables found")
        
        # Test imports
        try:
            import pandas as pd
            import numpy as np
            import yfinance as yf
            import plotly.graph_objects as go
            import rich
            from openai import OpenAI
            
            print_test_result("Dependencies", True, "All packages imported successfully")
            
        except ImportError as e:
            print_test_result("Dependencies", False, f"Import error: {e}")
            return False
        
        return True
        
    except Exception as e:
        print_test_result("Environment Setup", False, str(e))
        return False

def test_advanced_data_provider():
    """Test 2: Advanced Data Provider"""
    print_test_header("Advanced Data Provider Test")
    
    try:
        from src.market_data.data_provider import AdvancedDataProvider, MarketDataConfig
        
        # Initialize data provider
        config = MarketDataConfig(primary_source="yahoo")
        data_provider = AdvancedDataProvider(config)
        print_test_result("Data Provider Initialization", True)
        
        # Test real-time data
        instruments = ['EUR_USD', 'GBP_USD']
        real_time_data = data_provider.get_real_time_data(instruments)
        
        if real_time_data and len(real_time_data) > 0:
            print_test_result("Real-time Data", True, f"Retrieved data for {len(real_time_data)} instruments")
            
            # Display sample data
            for instrument, data in real_time_data.items():
                print(f"   {instrument}: {data.get('mid', 0):.5f}")
        else:
            print_test_result("Real-time Data", False, "No real-time data retrieved")
            return False
        
        # Test multi-timeframe data
        timeframes = ['H1', 'H4']
        multi_data = data_provider.get_multi_timeframe_data('EUR_USD', timeframes)
        
        if multi_data and len(multi_data) > 0:
            print_test_result("Multi-timeframe Data", True, f"Retrieved {len(multi_data)} timeframes")
            
            for tf, data in multi_data.items():
                print(f"   {tf}: {len(data)} periods")
        else:
            print_test_result("Multi-timeframe Data", False, "No multi-timeframe data")
            return False
        
        # Test technical indicators
        if 'H1' in multi_data:
            h1_data = multi_data['H1']
            required_indicators = ['rsi', 'macd', 'ema_21', 'bb_upper', 'atr']
            missing_indicators = [ind for ind in required_indicators if ind not in h1_data.columns]
            
            if not missing_indicators:
                print_test_result("Technical Indicators", True, "All indicators calculated")
            else:
                print_test_result("Technical Indicators", False, f"Missing: {missing_indicators}")
                return False
        
        return True
        
    except Exception as e:
        print_test_result("Advanced Data Provider", False, str(e))
        return False

def test_ai_analyzer():
    """Test 3: Advanced AI Analyzer"""
    print_test_header("Advanced AI Analyzer Test")
    
    try:
        from src.ai_engine.advanced_ai_analyzer import AdvancedAIAnalyzer, MarketRegime
        from src.market_data.data_provider import AdvancedDataProvider
        
        # Initialize components
        ai_analyzer = AdvancedAIAnalyzer()
        data_provider = AdvancedDataProvider()
        print_test_result("AI Analyzer Initialization", True)
        
        # Get sample market data
        multi_data = data_provider.get_multi_timeframe_data('EUR_USD', ['H1', 'H4', 'D1'])
        real_time = data_provider.get_real_time_data(['EUR_USD'])
        
        if not multi_data or not real_time:
            print_test_result("Market Data for AI", False, "Insufficient market data")
            return False
        
        # Prepare comprehensive market data
        market_data = {
            'instrument': 'EUR_USD',
            'current_price': real_time['EUR_USD'],
            **multi_data
        }
        
        # Test comprehensive analysis
        print("   Running comprehensive AI analysis...")
        ai_result = ai_analyzer.comprehensive_analysis(market_data, ['EUR_USD'])
        
        # Validate AI response
        required_fields = ['primary_signal', 'confidence', 'market_regime', 'reasoning']
        missing_fields = [field for field in required_fields 
                         if not hasattr(ai_result, field) or getattr(ai_result, field) is None]
        
        if not missing_fields:
            print_test_result("AI Analysis Structure", True, "All required fields present")
        else:
            print_test_result("AI Analysis Structure", False, f"Missing: {missing_fields}")
            return False
        
        # Validate signal values
        if ai_result.primary_signal in ['BUY', 'SELL', 'HOLD']:
            print_test_result("Signal Validation", True, f"Signal: {ai_result.primary_signal}")
        else:
            print_test_result("Signal Validation", False, f"Invalid signal: {ai_result.primary_signal}")
            return False
        
        # Validate confidence
        if 0 <= ai_result.confidence <= 1:
            print_test_result("Confidence Validation", True, f"Confidence: {ai_result.confidence:.2f}")
        else:
            print_test_result("Confidence Validation", False, f"Invalid confidence: {ai_result.confidence}")
            return False
        
        # Display analysis summary
        print(f"   Market Regime: {ai_result.market_regime.value}")
        print(f"   Strategy Recommendations: {', '.join(ai_result.strategy_recommendations)}")
        print(f"   Risk Level: {ai_result.risk_assessment.get('overall_risk', 'UNKNOWN')}")
        print(f"   Sentiment Score: {ai_result.sentiment_score:.2f}")
        
        return True
        
    except Exception as e:
        print_test_result("AI Analyzer", False, str(e))
        return False

def test_trading_strategies():
    """Test 4: Advanced Trading Strategies"""
    print_test_header("Advanced Trading Strategies Test")
    
    try:
        from src.strategies.scalping_strategy import ScalpingStrategy
        from src.strategies.day_trading_strategy import DayTradingStrategy
        from src.strategies.trend_following_strategy import TrendFollowingStrategy
        from src.market_data.data_provider import AdvancedDataProvider
        
        # Initialize strategies
        strategies = {
            'Scalping': ScalpingStrategy(),
            'Day Trading': DayTradingStrategy(),
            'Trend Following': TrendFollowingStrategy()
        }
        
        print_test_result("Strategy Initialization", True, f"Initialized {len(strategies)} strategies")
        
        # Get market data
        data_provider = AdvancedDataProvider()
        multi_data = data_provider.get_multi_timeframe_data('EUR_USD', ['M1', 'M5', 'M15', 'H1', 'H4', 'D1'])
        real_time = data_provider.get_real_time_data(['EUR_USD'])
        
        if not multi_data or not real_time:
            print_test_result("Market Data for Strategies", False, "Insufficient market data")
            return False
        
        # Prepare market data
        market_data = {
            'instrument': 'EUR_USD',
            'current_price': real_time['EUR_USD'],
            **multi_data
        }
        
        # Test each strategy
        strategy_results = {}
        for name, strategy in strategies.items():
            try:
                # Test signal generation
                signal = strategy.analyze(market_data)
                
                if signal and hasattr(signal, 'signal_type') and hasattr(signal, 'confidence'):
                    strategy_results[name] = {
                        'signal': signal.signal_type,
                        'confidence': signal.confidence,
                        'success': True
                    }
                    print_test_result(f"{name} Strategy", True, 
                                    f"Signal: {signal.signal_type}, Confidence: {signal.confidence:.2f}")
                else:
                    strategy_results[name] = {'success': False}
                    print_test_result(f"{name} Strategy", False, "Invalid signal structure")
                
            except Exception as e:
                strategy_results[name] = {'success': False, 'error': str(e)}
                print_test_result(f"{name} Strategy", False, str(e))
        
        # Check if at least one strategy worked
        successful_strategies = sum(1 for result in strategy_results.values() if result.get('success'))
        
        if successful_strategies > 0:
            print_test_result("Overall Strategy Test", True, f"{successful_strategies}/{len(strategies)} strategies working")
            return True
        else:
            print_test_result("Overall Strategy Test", False, "No strategies working")
            return False
        
    except Exception as e:
        print_test_result("Trading Strategies", False, str(e))
        return False

def test_backtesting_engine():
    """Test 5: Backtesting Engine"""
    print_test_header("Backtesting Engine Test")
    
    try:
        from src.backtesting.backtest_engine import AdvancedBacktester, Trade
        from src.strategies.day_trading_strategy import DayTradingStrategy
        from src.market_data.data_provider import AdvancedDataProvider
        
        # Initialize backtester
        backtester = AdvancedBacktester(
            initial_capital=10000.0,
            commission=0.0001,
            spread=0.0002
        )
        print_test_result("Backtester Initialization", True)
        
        # Test trade creation
        trade = Trade(
            entry_time=datetime.now(),
            instrument='EUR_USD',
            direction='BUY',
            entry_price=1.0850,
            quantity=1000,
            commission=1.085
        )
        
        # Test P&L calculation
        trade.exit_price = 1.0870
        pnl = trade.calculate_pnl()
        
        if pnl > 0:
            print_test_result("Trade P&L Calculation", True, f"P&L: ${pnl:.2f}")
        else:
            print_test_result("Trade P&L Calculation", False, f"Unexpected P&L: ${pnl:.2f}")
        
        # Test with sample data (simplified)
        print("   Testing with sample market data...")
        
        # Create sample data
        import pandas as pd
        dates = pd.date_range(start='2024-01-01', end='2024-01-07', freq='H')
        sample_data = pd.DataFrame({
            'open': 1.0850 + np.random.randn(len(dates)) * 0.001,
            'high': 1.0850 + np.random.randn(len(dates)) * 0.001 + 0.0005,
            'low': 1.0850 + np.random.randn(len(dates)) * 0.001 - 0.0005,
            'close': 1.0850 + np.random.randn(len(dates)) * 0.001,
            'volume': np.random.randint(1000, 10000, len(dates))
        }, index=dates)
        
        # Add basic indicators
        sample_data['rsi'] = 50 + np.random.randn(len(dates)) * 10
        sample_data['macd'] = np.random.randn(len(dates)) * 0.0001
        sample_data['macd_signal'] = sample_data['macd'] * 0.8
        sample_data['ema_21'] = sample_data['close'].ewm(span=21).mean()
        sample_data['ema_50'] = sample_data['close'].ewm(span=50).mean()
        sample_data['atr'] = 0.001
        sample_data['adx'] = 25 + np.random.randn(len(dates)) * 5
        sample_data['plus_di'] = 25 + np.random.randn(len(dates)) * 5
        sample_data['minus_di'] = 25 + np.random.randn(len(dates)) * 5
        
        market_data = {'EUR_USD': sample_data}
        strategies = [DayTradingStrategy()]
        
        # Run mini backtest
        try:
            results = backtester.run_backtest(
                market_data, 
                strategies,
                datetime(2024, 1, 1),
                datetime(2024, 1, 3)
            )
            
            print_test_result("Backtest Execution", True, 
                            f"Completed with {results.total_trades} trades")
            
            if hasattr(results, 'total_pnl'):
                print(f"   Total P&L: ${results.total_pnl:.2f}")
                print(f"   Win Rate: {results.win_rate:.1%}")
                print(f"   Max Drawdown: ${results.max_drawdown:.2f}")
            
        except Exception as e:
            print_test_result("Backtest Execution", False, str(e))
            return False
        
        return True
        
    except Exception as e:
        print_test_result("Backtesting Engine", False, str(e))
        return False

def test_cli_interface():
    """Test 6: CLI Interface"""
    print_test_header("CLI Interface Test")
    
    try:
        from src.cli.beautiful_cli import BeautifulCLI
        
        # Initialize CLI
        cli = BeautifulCLI()
        print_test_result("CLI Initialization", True)
        
        # Test data updates
        cli.update_market_data({
            'EUR_USD': {
                'current_price': {'mid': 1.0850, 'bid': 1.0849, 'ask': 1.0851, 'spread': 0.0002}
            }
        })
        
        cli.add_signal({
            'instrument': 'EUR_USD',
            'signal_type': 'BUY',
            'confidence': 0.85,
            'strategy': 'Test'
        })
        
        cli.update_performance({
            'daily_pnl': 125.50,
            'win_rate': 0.68,
            'total_trades': 24
        })
        
        print_test_result("CLI Data Updates", True, "All data updates successful")
        
        # Test layout creation (without starting the live interface)
        try:
            cli._update_all_panels()
            print_test_result("CLI Panel Updates", True, "All panels updated successfully")
        except Exception as e:
            print_test_result("CLI Panel Updates", False, str(e))
            return False
        
        return True
        
    except Exception as e:
        print_test_result("CLI Interface", False, str(e))
        return False

def test_integration():
    """Test 7: System Integration"""
    print_test_header("System Integration Test")
    
    try:
        print("   Testing component integration...")
        
        # Test importing main application
        try:
            from advanced_main import AdvancedTradingBot
            print_test_result("Main Application Import", True)
        except Exception as e:
            print_test_result("Main Application Import", False, str(e))
            return False
        
        # Test bot initialization
        try:
            bot = AdvancedTradingBot()
            print_test_result("Bot Initialization", True)
        except Exception as e:
            print_test_result("Bot Initialization", False, str(e))
            return False
        
        # Test component access
        components = ['data_provider', 'ai_analyzer', 'strategies', 'cli']
        for component in components:
            if hasattr(bot, component):
                print_test_result(f"{component.title()} Component", True)
            else:
                print_test_result(f"{component.title()} Component", False, "Component not found")
                return False
        
        print_test_result("Integration Test", True, "All components integrated successfully")
        return True
        
    except Exception as e:
        print_test_result("System Integration", False, str(e))
        return False

def main():
    """Run all tests"""
    logger = setup_test_logging()
    
    print(f"{Fore.CYAN}🧪 Advanced Forex Trading Bot - System Test Suite{Style.RESET_ALL}")
    print("=" * 70)
    print("This comprehensive test validates all advanced components.")
    print("Make sure you have configured config.env with your API keys.")
    print()
    
    # Check if config file exists
    if not os.path.exists('config.env'):
        print(f"{Fore.RED}❌ config.env file not found{Style.RESET_ALL}")
        print("Please copy config.env.example to config.env and add your API keys")
        return False
    
    # Run all tests
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Advanced Data Provider", test_advanced_data_provider),
        ("AI Analyzer", test_ai_analyzer),
        ("Trading Strategies", test_trading_strategies),
        ("Backtesting Engine", test_backtesting_engine),
        ("CLI Interface", test_cli_interface),
        ("System Integration", test_integration)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        try:
            if test_function():
                passed_tests += 1
            else:
                print(f"\n{Fore.RED}⚠️  Test failed: {test_name}{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}💥 Test crashed: {test_name} - {e}{Style.RESET_ALL}")
    
    # Final results
    print(f"\n{Fore.CYAN}📊 Test Results{Style.RESET_ALL}")
    print("=" * 70)
    print(f"Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print(f"{Fore.GREEN}🎉 All tests passed! Your advanced trading bot is ready.{Style.RESET_ALL}")
        print(f"\n💡 Next steps:")
        print(f"   1. Run live mode: python advanced_main.py --mode live")
        print(f"   2. Run backtest: python advanced_main.py --mode backtest --start-date 2024-01-01 --end-date 2024-01-31")
        print(f"   3. Check the beautiful CLI interface")
        return True
    else:
        print(f"{Fore.RED}❌ Some tests failed. Please fix the issues before using the bot.{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

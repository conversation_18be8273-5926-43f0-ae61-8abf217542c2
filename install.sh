#!/bin/bash
# Advanced Forex Trading Bot - Installation Script
# Handles common installation issues including TA-Lib

set -e  # Exit on any error

echo "🚀 Advanced Forex Trading Bot - Installation Script"
echo "=================================================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Try to install basic requirements first
echo "📦 Installing basic requirements..."
if pip install -r requirements_basic.txt; then
    echo "✅ Basic requirements installed successfully"
    BASIC_INSTALL=true
else
    echo "❌ Failed to install basic requirements"
    exit 1
fi

# Try to install TA-Lib if possible
echo "🔧 Attempting to install TA-Lib (optional)..."
if command -v brew &> /dev/null; then
    echo "🍺 Homebrew detected, installing TA-Lib via brew..."
    if brew install ta-lib; then
        echo "✅ TA-Lib system library installed"
        if pip install ta-lib; then
            echo "✅ TA-Lib Python package installed"
        else
            echo "⚠️  TA-Lib Python package failed, continuing without it"
        fi
    else
        echo "⚠️  TA-Lib brew installation failed, continuing without it"
    fi
elif command -v apt-get &> /dev/null; then
    echo "🐧 Ubuntu/Debian detected, attempting TA-Lib installation..."
    echo "   This may require sudo privileges..."
    if sudo apt-get update && sudo apt-get install -y build-essential wget; then
        # Download and install TA-Lib
        if [ ! -f "ta-lib-0.4.0-src.tar.gz" ]; then
            wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
        fi
        
        if tar -xzf ta-lib-0.4.0-src.tar.gz; then
            cd ta-lib/
            if ./configure --prefix=/usr && make && sudo make install; then
                cd ..
                echo "✅ TA-Lib system library installed"
                if pip install ta-lib; then
                    echo "✅ TA-Lib Python package installed"
                else
                    echo "⚠️  TA-Lib Python package failed, continuing without it"
                fi
            else
                cd ..
                echo "⚠️  TA-Lib compilation failed, continuing without it"
            fi
        else
            echo "⚠️  TA-Lib download/extraction failed, continuing without it"
        fi
    else
        echo "⚠️  Could not install build dependencies, continuing without TA-Lib"
    fi
else
    echo "⚠️  Unknown system, skipping TA-Lib installation"
    echo "   You can install it manually if needed"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p src logs data backtest_results
echo "✅ Directories created"

# Copy configuration template
if [ ! -f "config.env" ]; then
    if [ -f "config.env.example" ]; then
        echo "📝 Creating config.env from template..."
        cp config.env.example config.env
        echo "✅ Configuration file created"
        echo "⚠️  Please edit config.env with your API keys"
    else
        echo "⚠️  config.env.example not found, creating basic config..."
        cat > config.env << EOF
# Advanced Forex Trading Bot Configuration

# Required: OpenAI API Key (get from https://platform.openai.com/)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Optional: OANDA API (for live trading - use practice account)
OANDA_API_KEY=your_oanda_api_key
OANDA_ACCOUNT_ID=your_account_id
OANDA_ENVIRONMENT=practice

# Trading Configuration
DEFAULT_CURRENCY_PAIR=EUR_USD,GBP_USD,USD_JPY
ANALYSIS_INTERVAL_MINUTES=5
LOG_LEVEL=INFO

# Risk Management
MAX_RISK_PER_TRADE=2.0
MAX_DAILY_LOSS=500
MAX_OPEN_POSITIONS=5
EOF
        echo "✅ Basic configuration file created"
    fi
else
    echo "✅ Configuration file already exists"
fi

# Make scripts executable
echo "🔧 Making scripts executable..."
chmod +x advanced_main.py test_advanced_system.py quick_test.py

# Run quick test
echo "🧪 Running quick validation test..."
if python3 quick_test.py; then
    echo "✅ Quick test passed"
else
    echo "⚠️  Quick test had issues, but installation may still work"
fi

echo ""
echo "🎉 Installation completed!"
echo "=================================================="
echo ""
echo "📋 Next steps:"
echo "1. Edit config.env with your OpenAI API key:"
echo "   nano config.env"
echo ""
echo "2. Activate the virtual environment:"
echo "   source venv/bin/activate"
echo ""
echo "3. Test the system:"
echo "   python3 test_advanced_system.py"
echo ""
echo "4. Run the bot:"
echo "   python3 advanced_main.py --mode live"
echo ""
echo "📚 Documentation:"
echo "   - ADVANCED_README.md - Complete documentation"
echo "   - INSTALLATION_GUIDE.md - Detailed installation guide"
echo ""
echo "⚠️  Important:"
echo "   - This system is for educational purposes"
echo "   - Always test with demo accounts first"
echo "   - OpenAI API usage incurs costs"
echo ""
echo "🆘 If you encounter issues:"
echo "   - Check INSTALLATION_GUIDE.md for troubleshooting"
echo "   - Run: python3 quick_test.py for basic validation"
echo ""

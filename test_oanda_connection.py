#!/usr/bin/env python3
"""
OANDA Connection Test Script
This script tests your OANDA API connection and displays account information
"""

import os
import sys
from dotenv import load_dotenv
from colorama import init, Fore, Style
import oandapyV20
import oandapyV20.endpoints.accounts as accounts
import oandapyV20.endpoints.pricing as pricing

# Initialize colorama
init(autoreset=True)

def test_oanda_connection():
    """Test OANDA API connection and display account info"""
    
    print(f"{Fore.CYAN}🔧 Testing OANDA Connection...{Style.RESET_ALL}")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv('config.env')
    
    # Get credentials
    api_key = os.getenv('OANDA_API_KEY')
    account_id = os.getenv('OANDA_ACCOUNT_ID')
    environment = os.getenv('OANDA_ENVIRONMENT', 'practice')
    
    # Check if credentials are provided
    if not api_key:
        print(f"{Fore.RED}❌ OANDA_API_KEY not found in config.env{Style.RESET_ALL}")
        return False
    
    if not account_id:
        print(f"{Fore.RED}❌ OANDA_ACCOUNT_ID not found in config.env{Style.RESET_ALL}")
        return False
    
    print(f"{Fore.GREEN}✅ API Key found: {api_key[:10]}...{Style.RESET_ALL}")
    print(f"{Fore.GREEN}✅ Account ID: {account_id}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}✅ Environment: {environment}{Style.RESET_ALL}")
    
    try:
        # Create OANDA client
        if environment == 'practice':
            client = oandapyV20.API(access_token=api_key, environment="practice")
        else:
            client = oandapyV20.API(access_token=api_key, environment="live")
        
        print(f"\n{Fore.YELLOW}🔍 Testing API connection...{Style.RESET_ALL}")
        
        # Test 1: Get account information
        account_request = accounts.AccountDetails(account_id)
        account_response = client.request(account_request)
        
        account_info = account_response['account']
        
        print(f"{Fore.GREEN}✅ Successfully connected to OANDA!{Style.RESET_ALL}")
        print(f"\n📊 Account Information:")
        print(f"   Account ID: {account_info['id']}")
        print(f"   Currency: {account_info['currency']}")
        print(f"   Balance: {float(account_info['balance']):,.2f} {account_info['currency']}")
        print(f"   Unrealized P&L: {float(account_info['unrealizedPL']):,.2f} {account_info['currency']}")
        print(f"   Open Trades: {account_info['openTradeCount']}")
        print(f"   Open Positions: {account_info['openPositionCount']}")
        
        # Test 2: Get current prices for EUR_USD
        print(f"\n{Fore.YELLOW}🔍 Testing price data...{Style.RESET_ALL}")
        
        price_request = pricing.PricingInfo(
            accountID=account_id,
            params={"instruments": "EUR_USD"}
        )
        price_response = client.request(price_request)
        
        if price_response['prices']:
            price_info = price_response['prices'][0]
            bid = float(price_info['bids'][0]['price'])
            ask = float(price_info['asks'][0]['price'])
            spread = ask - bid
            
            print(f"{Fore.GREEN}✅ Price data received!{Style.RESET_ALL}")
            print(f"   EUR/USD Bid: {bid:.5f}")
            print(f"   EUR/USD Ask: {ask:.5f}")
            print(f"   Spread: {spread:.5f}")
            print(f"   Time: {price_info['time']}")
        
        # Test 3: Check available instruments
        print(f"\n{Fore.YELLOW}🔍 Checking available instruments...{Style.RESET_ALL}")
        
        instruments_request = accounts.AccountInstruments(account_id)
        instruments_response = client.request(instruments_request)
        
        instruments = instruments_response['instruments']
        forex_pairs = [inst for inst in instruments if inst['type'] == 'CURRENCY']
        
        print(f"{Fore.GREEN}✅ Found {len(forex_pairs)} forex pairs available{Style.RESET_ALL}")
        print("   Popular pairs:")
        popular_pairs = ['EUR_USD', 'GBP_USD', 'USD_JPY', 'AUD_USD', 'USD_CAD']
        for pair in popular_pairs:
            if any(inst['name'] == pair for inst in forex_pairs):
                print(f"   ✅ {pair}")
            else:
                print(f"   ❌ {pair}")
        
        print(f"\n{Fore.GREEN}🎉 All tests passed! OANDA connection is working perfectly.{Style.RESET_ALL}")
        print(f"\n💡 You can now run the trading bot with: python main.py")
        
        return True
        
    except Exception as e:
        print(f"{Fore.RED}❌ Connection failed: {str(e)}{Style.RESET_ALL}")
        print(f"\n🔧 Troubleshooting tips:")
        print(f"   1. Check your API key is correct")
        print(f"   2. Verify your Account ID format (should include dashes)")
        print(f"   3. Make sure you're using the right environment (practice/live)")
        print(f"   4. Check your internet connection")
        print(f"   5. Verify your OANDA account is active")
        
        return False

def main():
    """Main function"""
    print(f"{Fore.CYAN}OANDA Connection Test{Style.RESET_ALL}")
    print("=" * 30)
    
    # Check if config file exists
    if not os.path.exists('config.env'):
        print(f"{Fore.RED}❌ config.env file not found{Style.RESET_ALL}")
        print(f"💡 Please copy config.env.example to config.env and add your API keys")
        return False
    
    success = test_oanda_connection()
    
    if success:
        print(f"\n{Fore.GREEN}✅ Setup complete! Ready to start trading.{Style.RESET_ALL}")
    else:
        print(f"\n{Fore.RED}❌ Setup incomplete. Please fix the issues above.{Style.RESET_ALL}")
    
    return success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

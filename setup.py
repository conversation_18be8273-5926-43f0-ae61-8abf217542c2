#!/usr/bin/env python3
"""
Setup script for Forex Trading Bot
This script helps you set up the trading bot environment
"""

import os
import sys
import subprocess
from pathlib import Path

def print_step(step_num, description):
    """Print a formatted step"""
    print(f"\n🔧 Step {step_num}: {description}")
    print("=" * 50)

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_requirements():
    """Install required packages"""
    try:
        # Check if we're in a virtual environment
        in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

        if not in_venv:
            print("⚠️  Not in a virtual environment. Creating one...")

            # Create virtual environment
            subprocess.check_call([sys.executable, "-m", "venv", "venv"])
            print("✅ Virtual environment created")

            # Get the correct pip path
            if os.name == 'nt':  # Windows
                pip_path = os.path.join("venv", "Scripts", "pip")
                python_path = os.path.join("venv", "Scripts", "python")
            else:  # Unix/Linux/Mac
                pip_path = os.path.join("venv", "bin", "pip")
                python_path = os.path.join("venv", "bin", "python")

            # Install requirements in virtual environment
            subprocess.check_call([python_path, "-m", "pip", "install", "-r", "requirements.txt"])

            print("✅ All packages installed successfully in virtual environment")
            print("💡 To activate the virtual environment:")
            if os.name == 'nt':
                print("   venv\\Scripts\\activate")
            else:
                print("   source venv/bin/activate")

        else:
            # We're already in a virtual environment
            # Try basic requirements first (without problematic packages)
            requirements_file = 'requirements_basic.txt' if os.path.exists('requirements_basic.txt') else 'requirements.txt'

            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
                print(f"✅ All packages installed successfully from {requirements_file}")
            except subprocess.CalledProcessError:
                if requirements_file == 'requirements.txt' and os.path.exists('requirements_basic.txt'):
                    print("⚠️  Full requirements failed, trying basic requirements...")
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements_basic.txt"])
                    print("✅ Basic packages installed successfully")
                    print("💡 Some advanced features may be limited without TA-Lib")
                else:
                    raise

        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ["src", "logs", "data", "backtest_results"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def setup_config():
    """Set up configuration file"""
    if not os.path.exists("config.env"):
        if os.path.exists("config.env.example"):
            print("📝 Creating config.env from example...")
            with open("config.env.example", "r") as example:
                content = example.read()
            with open("config.env", "w") as config:
                config.write(content)
            print("✅ config.env created")
            print("⚠️  Please edit config.env and add your API keys")
        else:
            print("❌ config.env.example not found")
            return False
    else:
        print("✅ config.env already exists")
    return True

def create_gitignore():
    """Create .gitignore file"""
    gitignore_content = """# Environment variables
config.env
.env

# Logs
logs/
*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.vscode/
.idea/
*.swp
*.swo

# Data files
data/
backtest_results/

# OS
.DS_Store
Thumbs.db
"""
    
    with open(".gitignore", "w") as f:
        f.write(gitignore_content)
    print("✅ .gitignore created")

def main():
    """Main setup function"""
    print("🚀 Setting up Forex Trading Bot Environment")
    print("=" * 50)
    
    # Step 1: Check Python version
    print_step(1, "Checking Python version")
    if not check_python_version():
        return False
    
    # Step 2: Create directories
    print_step(2, "Creating project directories")
    create_directories()
    
    # Step 3: Install requirements
    print_step(3, "Installing Python packages")
    if not install_requirements():
        return False
    
    # Step 4: Setup configuration
    print_step(4, "Setting up configuration")
    if not setup_config():
        return False
    
    # Step 5: Create .gitignore
    print_step(5, "Creating .gitignore")
    create_gitignore()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit config.env and add your API keys:")
    print("   - OANDA API key and account ID")
    print("   - OpenAI API key")
    print("2. Run: python main.py")
    print("\n💡 Need help getting API keys? Check the README.md file")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

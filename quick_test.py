#!/usr/bin/env python3
"""
Quick Test - Basic System Validation
Tests core functionality without external dependencies
"""

import os
import sys
import importlib.util

def test_file_structure():
    """Test if all required files exist"""
    print("🔍 Testing file structure...")
    
    required_files = [
        'advanced_main.py',
        'config.env.example',
        'requirements.txt',
        'ADVANCED_README.md',
        'src/strategies/base_strategy.py',
        'src/strategies/scalping_strategy.py',
        'src/strategies/day_trading_strategy.py',
        'src/strategies/trend_following_strategy.py',
        'src/market_data/data_provider.py',
        'src/ai_engine/advanced_ai_analyzer.py',
        'src/backtesting/backtest_engine.py',
        'src/cli/beautiful_cli.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("✅ All required files present")
        return True

def test_python_syntax():
    """Test if Python files have valid syntax"""
    print("\n🐍 Testing Python syntax...")
    
    python_files = [
        'advanced_main.py',
        'src/strategies/base_strategy.py',
        'src/strategies/scalping_strategy.py',
        'src/strategies/day_trading_strategy.py',
        'src/strategies/trend_following_strategy.py',
        'src/market_data/data_provider.py',
        'src/ai_engine/advanced_ai_analyzer.py',
        'src/backtesting/backtest_engine.py',
        'src/cli/beautiful_cli.py'
    ]
    
    syntax_errors = []
    for file_path in python_files:
        try:
            with open(file_path, 'r') as f:
                compile(f.read(), file_path, 'exec')
        except SyntaxError as e:
            syntax_errors.append(f"{file_path}: {e}")
        except FileNotFoundError:
            syntax_errors.append(f"{file_path}: File not found")
    
    if syntax_errors:
        print(f"❌ Syntax errors found:")
        for error in syntax_errors:
            print(f"   {error}")
        return False
    else:
        print("✅ All Python files have valid syntax")
        return True

def test_imports():
    """Test if core modules can be imported"""
    print("\n📦 Testing core imports...")
    
    # Test standard library imports
    try:
        import os
        import sys
        import logging
        import datetime
        import json
        print("✅ Standard library imports successful")
    except ImportError as e:
        print(f"❌ Standard library import error: {e}")
        return False
    
    # Test if our modules can be imported (basic syntax check)
    modules_to_test = [
        ('src.strategies.base_strategy', 'BaseStrategy'),
        ('src.market_data.data_provider', 'AdvancedDataProvider'),
        ('src.ai_engine.advanced_ai_analyzer', 'AdvancedAIAnalyzer'),
        ('src.backtesting.backtest_engine', 'AdvancedBacktester'),
        ('src.cli.beautiful_cli', 'BeautifulCLI')
    ]
    
    importable_modules = 0
    for module_name, class_name in modules_to_test:
        try:
            # Add src to path temporarily
            if 'src' not in sys.path:
                sys.path.insert(0, 'src')
            
            spec = importlib.util.spec_from_file_location(
                module_name, 
                module_name.replace('.', '/') + '.py'
            )
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                
                if hasattr(module, class_name):
                    importable_modules += 1
                    print(f"✅ {module_name}.{class_name} - OK")
                else:
                    print(f"❌ {module_name}.{class_name} - Class not found")
            else:
                print(f"❌ {module_name} - Cannot load module")
                
        except Exception as e:
            print(f"❌ {module_name} - Import error: {e}")
    
    if importable_modules == len(modules_to_test):
        print("✅ All core modules importable")
        return True
    else:
        print(f"⚠️  {importable_modules}/{len(modules_to_test)} modules importable")
        return importable_modules > 0

def test_configuration():
    """Test configuration files"""
    print("\n⚙️ Testing configuration...")
    
    # Check if example config exists
    if not os.path.exists('config.env.example'):
        print("❌ config.env.example not found")
        return False
    
    # Check if requirements.txt exists and has content
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found")
        return False
    
    try:
        with open('requirements.txt', 'r') as f:
            requirements = f.read().strip()
            if len(requirements) > 0:
                req_count = len([line for line in requirements.split('\n') if line.strip() and not line.startswith('#')])
                print(f"✅ requirements.txt contains {req_count} packages")
            else:
                print("❌ requirements.txt is empty")
                return False
    except Exception as e:
        print(f"❌ Error reading requirements.txt: {e}")
        return False
    
    print("✅ Configuration files OK")
    return True

def test_documentation():
    """Test documentation files"""
    print("\n📚 Testing documentation...")
    
    doc_files = ['README.md', 'ADVANCED_README.md', 'QUICK_START_GUIDE.md']
    existing_docs = []
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            existing_docs.append(doc_file)
    
    if existing_docs:
        print(f"✅ Documentation files: {', '.join(existing_docs)}")
        return True
    else:
        print("❌ No documentation files found")
        return False

def main():
    """Run all quick tests"""
    print("🚀 Advanced Forex Trading Bot - Quick Test Suite")
    print("=" * 60)
    print("Testing core system without external dependencies...")
    print()
    
    tests = [
        ("File Structure", test_file_structure),
        ("Python Syntax", test_python_syntax),
        ("Core Imports", test_imports),
        ("Configuration", test_configuration),
        ("Documentation", test_documentation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        try:
            if test_function():
                passed_tests += 1
        except Exception as e:
            print(f"💥 {test_name} crashed: {e}")
    
    print(f"\n📊 Quick Test Results")
    print("=" * 60)
    print(f"Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 All quick tests passed!")
        print("\n💡 Next steps:")
        print("   1. Install dependencies: python3 setup.py")
        print("   2. Configure API keys: cp config.env.example config.env")
        print("   3. Run full tests: python3 test_advanced_system.py")
        print("   4. Start the bot: python3 advanced_main.py --mode live")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

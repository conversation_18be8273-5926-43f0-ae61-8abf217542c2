#!/usr/bin/env python3
"""
Complete System Test Script
Tests all components of the forex trading bot to ensure everything works together
"""

import os
import sys
import time
from dotenv import load_dotenv
from colorama import init, Fore, Style
import logging

# Initialize colorama
init(autoreset=True)

def setup_test_logging():
    """Set up logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def print_test_header(test_name):
    """Print a formatted test header"""
    print(f"\n{Fore.CYAN}🧪 {test_name}{Style.RESET_ALL}")
    print("=" * 60)

def print_test_result(test_name, success, details=""):
    """Print test result"""
    if success:
        print(f"{Fore.GREEN}✅ {test_name}: PASSED{Style.RESET_ALL}")
        if details:
            print(f"   {details}")
    else:
        print(f"{Fore.RED}❌ {test_name}: FAILED{Style.RESET_ALL}")
        if details:
            print(f"   {details}")

def test_environment_setup():
    """Test 1: Environment and Configuration"""
    print_test_header("Environment and Configuration Test")
    
    try:
        # Load environment variables
        load_dotenv('config.env')
        
        # Check required environment variables
        required_vars = [
            'OANDA_API_KEY',
            'OANDA_ACCOUNT_ID',
            'OPENAI_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print_test_result("Environment Variables", False, f"Missing: {', '.join(missing_vars)}")
            return False
        
        print_test_result("Environment Variables", True, "All required variables found")
        
        # Test configuration values
        config_tests = [
            ('OANDA_ENVIRONMENT', os.getenv('OANDA_ENVIRONMENT', 'practice')),
            ('DEFAULT_CURRENCY_PAIR', os.getenv('DEFAULT_CURRENCY_PAIR', 'EUR_USD')),
            ('MAX_RISK_PER_TRADE', os.getenv('MAX_RISK_PER_TRADE', '2')),
            ('MAX_DAILY_LOSS', os.getenv('MAX_DAILY_LOSS', '100')),
        ]
        
        for config_name, config_value in config_tests:
            print(f"   {config_name}: {config_value}")
        
        print_test_result("Configuration", True, "All configuration values loaded")
        return True
        
    except Exception as e:
        print_test_result("Environment Setup", False, str(e))
        return False

def test_data_fetcher():
    """Test 2: Data Fetcher Component"""
    print_test_header("Data Fetcher Component Test")
    
    try:
        from src.data_fetcher import DataFetcher
        
        # Initialize data fetcher
        data_fetcher = DataFetcher()
        print_test_result("Data Fetcher Initialization", True)
        
        # Test current price fetching
        current_price = data_fetcher.get_current_price('EUR_USD')
        if current_price and 'bid' in current_price and 'ask' in current_price:
            print_test_result("Current Price Fetch", True, 
                            f"EUR/USD: {current_price['bid']:.5f}/{current_price['ask']:.5f}")
        else:
            print_test_result("Current Price Fetch", False, "Invalid price data")
            return False
        
        # Test historical data
        historical_data = data_fetcher.get_historical_data('EUR_USD', 'H1', 10)
        if len(historical_data) > 0:
            print_test_result("Historical Data Fetch", True, f"Retrieved {len(historical_data)} candles")
        else:
            print_test_result("Historical Data Fetch", False, "No historical data")
            return False
        
        # Test technical indicators
        historical_data = data_fetcher.calculate_technical_indicators(historical_data)
        required_indicators = ['sma_20', 'rsi', 'macd', 'bb_upper', 'bb_lower']
        missing_indicators = [ind for ind in required_indicators if ind not in historical_data.columns]
        
        if not missing_indicators:
            print_test_result("Technical Indicators", True, "All indicators calculated")
        else:
            print_test_result("Technical Indicators", False, f"Missing: {missing_indicators}")
            return False
        
        # Test market analysis data
        analysis_data = data_fetcher.get_market_analysis_data('EUR_USD')
        if analysis_data and 'current_price' in analysis_data and 'h1_analysis' in analysis_data:
            print_test_result("Market Analysis Data", True, "Complete analysis data retrieved")
        else:
            print_test_result("Market Analysis Data", False, "Incomplete analysis data")
            return False
        
        return True
        
    except Exception as e:
        print_test_result("Data Fetcher", False, str(e))
        return False

def test_ai_analyzer():
    """Test 3: AI Analyzer Component"""
    print_test_header("AI Analyzer Component Test")
    
    try:
        from src.ai_analyzer import AIAnalyzer
        from src.data_fetcher import DataFetcher
        
        # Initialize components
        ai_analyzer = AIAnalyzer()
        data_fetcher = DataFetcher()
        print_test_result("AI Analyzer Initialization", True)
        
        # Get sample market data
        market_data = data_fetcher.get_market_analysis_data('EUR_USD')
        market_data['instrument'] = 'EUR_USD'
        
        # Test AI analysis
        ai_signal = ai_analyzer.analyze_market_data(market_data)
        
        # Validate AI response
        required_fields = ['signal', 'confidence', 'reasoning', 'entry_price']
        missing_fields = [field for field in required_fields if field not in ai_signal]
        
        if not missing_fields:
            print_test_result("AI Analysis Structure", True, "All required fields present")
        else:
            print_test_result("AI Analysis Structure", False, f"Missing: {missing_fields}")
            return False
        
        # Validate signal values
        if ai_signal['signal'] in ['BUY', 'SELL', 'HOLD']:
            print_test_result("Signal Validation", True, f"Signal: {ai_signal['signal']}")
        else:
            print_test_result("Signal Validation", False, f"Invalid signal: {ai_signal['signal']}")
            return False
        
        # Validate confidence
        if 0 <= ai_signal['confidence'] <= 1:
            print_test_result("Confidence Validation", True, f"Confidence: {ai_signal['confidence']:.2f}")
        else:
            print_test_result("Confidence Validation", False, f"Invalid confidence: {ai_signal['confidence']}")
            return False
        
        # Test market sentiment
        sentiment = ai_analyzer.get_market_sentiment(['EUR_USD', 'GBP_USD'])
        if sentiment and 'sentiment' in sentiment:
            print_test_result("Market Sentiment", True, "Sentiment analysis working")
        else:
            print_test_result("Market Sentiment", False, "Sentiment analysis failed")
            return False
        
        print(f"   AI Signal: {ai_signal['signal']} (Confidence: {ai_signal['confidence']:.2f})")
        print(f"   Reasoning: {ai_signal['reasoning'][:100]}...")
        
        return True
        
    except Exception as e:
        print_test_result("AI Analyzer", False, str(e))
        return False

def test_trade_executor():
    """Test 4: Trade Executor Component"""
    print_test_header("Trade Executor Component Test")
    
    try:
        from src.trade_executor import TradeExecutor
        
        # Initialize trade executor
        trade_executor = TradeExecutor()
        print_test_result("Trade Executor Initialization", True)
        
        # Test account summary
        account_summary = trade_executor.get_account_summary()
        if account_summary and 'balance' in account_summary:
            print_test_result("Account Summary", True, 
                            f"Balance: {account_summary['balance']:,.2f} {account_summary.get('currency', 'USD')}")
        else:
            print_test_result("Account Summary", False, "Failed to get account summary")
            return False
        
        # Test open positions
        positions = trade_executor.get_open_positions()
        print_test_result("Open Positions", True, f"Found {len(positions)} open positions")
        
        # Test open trades
        trades = trade_executor.get_open_trades()
        print_test_result("Open Trades", True, f"Found {len(trades)} open trades")
        
        # Display account info
        print(f"   Account ID: {account_summary.get('account_id', 'N/A')}")
        print(f"   Balance: {account_summary.get('balance', 0):,.2f}")
        print(f"   Unrealized P&L: {account_summary.get('unrealized_pl', 0):,.2f}")
        print(f"   Margin Used: {account_summary.get('margin_used', 0):,.2f}")
        print(f"   Open Positions: {len(positions)}")
        
        return True
        
    except Exception as e:
        print_test_result("Trade Executor", False, str(e))
        return False

def test_risk_manager():
    """Test 5: Risk Manager Component"""
    print_test_header("Risk Manager Component Test")
    
    try:
        from src.risk_manager import RiskManager
        from src.trade_executor import TradeExecutor
        
        # Initialize components
        risk_manager = RiskManager()
        trade_executor = TradeExecutor()
        print_test_result("Risk Manager Initialization", True)
        
        # Get account data
        account_summary = trade_executor.get_account_summary()
        current_positions = trade_executor.get_open_positions()
        
        # Test trading allowance check
        trading_allowed, reason = risk_manager.check_trading_allowed(account_summary, current_positions)
        print_test_result("Trading Allowance Check", True, f"Allowed: {trading_allowed}, Reason: {reason}")
        
        # Test position size calculation
        balance = account_summary.get('balance', 10000)
        position_size, risk_info = risk_manager.calculate_position_size(
            balance, 1.1000, 1.0980, 'EUR_USD', 0.8
        )
        
        if position_size > 0 and 'risk_percentage' in risk_info:
            print_test_result("Position Size Calculation", True, 
                            f"Size: {position_size}, Risk: {risk_info['risk_percentage']:.2f}%")
        else:
            print_test_result("Position Size Calculation", False, "Invalid calculation")
            return False
        
        # Test signal validation
        test_signal = {
            'signal': 'BUY',
            'confidence': 0.8,
            'entry_price': 1.1000,
            'stop_loss': 1.0980,
            'take_profit': 1.1040,
            'instrument': 'EUR_USD'
        }
        
        is_valid, validated_signal = risk_manager.validate_trade_signal(
            test_signal, account_summary, current_positions
        )
        
        print_test_result("Signal Validation", is_valid, 
                        "Signal validated" if is_valid else validated_signal.get('error', 'Unknown error'))
        
        # Test risk summary
        risk_summary = risk_manager.get_risk_summary(account_summary)
        if risk_summary:
            print_test_result("Risk Summary", True, "Risk summary generated")
            print(f"   Max Risk per Trade: {risk_summary.get('max_risk_per_trade', 0):.1f}%")
            print(f"   Daily Trades: {risk_summary.get('daily_trades', 0)}")
            print(f"   Open Positions: {risk_summary.get('open_positions', 0)}")
        else:
            print_test_result("Risk Summary", False, "Failed to generate risk summary")
            return False
        
        return True
        
    except Exception as e:
        print_test_result("Risk Manager", False, str(e))
        return False

def test_integration():
    """Test 6: Component Integration"""
    print_test_header("Component Integration Test")
    
    try:
        from src.data_fetcher import DataFetcher
        from src.ai_analyzer import AIAnalyzer
        from src.trade_executor import TradeExecutor
        from src.risk_manager import RiskManager
        
        # Initialize all components
        data_fetcher = DataFetcher()
        ai_analyzer = AIAnalyzer()
        trade_executor = TradeExecutor()
        risk_manager = RiskManager()
        
        print_test_result("Component Initialization", True, "All components initialized")
        
        # Test complete workflow (without actual trading)
        print("   Testing complete analysis workflow...")
        
        # 1. Get market data
        market_data = data_fetcher.get_market_analysis_data('EUR_USD')
        market_data['instrument'] = 'EUR_USD'
        
        # 2. Get AI analysis
        ai_signal = ai_analyzer.analyze_market_data(market_data)
        ai_signal['instrument'] = 'EUR_USD'
        
        # 3. Get account info
        account_summary = trade_executor.get_account_summary()
        current_positions = trade_executor.get_open_positions()
        
        # 4. Validate with risk manager
        is_valid, validated_signal = risk_manager.validate_trade_signal(
            ai_signal, account_summary, current_positions
        )
        
        print_test_result("Complete Workflow", True, 
                        f"Signal: {ai_signal['signal']}, Valid: {is_valid}")
        
        # Test bot controller initialization
        from src.bot_controller import BotController
        
        bot = BotController(data_fetcher, ai_analyzer, trade_executor, risk_manager)
        print_test_result("Bot Controller", True, "Bot controller initialized successfully")
        
        return True
        
    except Exception as e:
        print_test_result("Integration Test", False, str(e))
        return False

def main():
    """Run all tests"""
    logger = setup_test_logging()
    
    print(f"{Fore.CYAN}🧪 Forex Trading Bot - Complete System Test{Style.RESET_ALL}")
    print("=" * 60)
    print("This test will validate all components of the trading bot.")
    print("Make sure you have configured config.env with your API keys.")
    print()
    
    # Check if config file exists
    if not os.path.exists('config.env'):
        print(f"{Fore.RED}❌ config.env file not found{Style.RESET_ALL}")
        print("Please copy config.env.example to config.env and add your API keys")
        return False
    
    # Run all tests
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Data Fetcher", test_data_fetcher),
        ("AI Analyzer", test_ai_analyzer),
        ("Trade Executor", test_trade_executor),
        ("Risk Manager", test_risk_manager),
        ("Integration", test_integration)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        try:
            if test_function():
                passed_tests += 1
            else:
                print(f"\n{Fore.RED}⚠️  Test failed: {test_name}{Style.RESET_ALL}")
        except Exception as e:
            print(f"\n{Fore.RED}💥 Test crashed: {test_name} - {e}{Style.RESET_ALL}")
    
    # Final results
    print(f"\n{Fore.CYAN}📊 Test Results{Style.RESET_ALL}")
    print("=" * 60)
    print(f"Passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print(f"{Fore.GREEN}🎉 All tests passed! Your trading bot is ready to use.{Style.RESET_ALL}")
        print(f"\n💡 Next steps:")
        print(f"   1. Run: python main.py")
        print(f"   2. Monitor the logs in the logs/ directory")
        print(f"   3. Start with demo trading to validate performance")
        return True
    else:
        print(f"{Fore.RED}❌ Some tests failed. Please fix the issues before running the bot.{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)

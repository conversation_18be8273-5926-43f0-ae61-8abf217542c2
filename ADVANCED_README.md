# 🚀 Advanced AI Forex Trading Bot

**Professional-grade AI-powered forex trading system with advanced features**

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![AI Powered](https://img.shields.io/badge/AI-Powered-green.svg)](https://openai.com/)

## 🌟 Features

### 🤖 **Advanced AI Analysis**
- **Multi-Model Ensemble**: 5 specialized AI analysts (Technical, Quantitative, Macro, Sentiment, Risk)
- **Market Regime Detection**: Automatically identifies trending, ranging, volatile, breakout, and reversal markets
- **Pattern Recognition**: Advanced chart pattern detection and analysis
- **Sentiment Analysis**: Real-time market sentiment evaluation
- **Probability Forecasting**: Statistical probability distributions for trade outcomes

### 📈 **Professional Trading Strategies**
- **Scalping Strategy**: High-frequency trading with 1-5 minute timeframes
- **Day Trading Strategy**: Intraday trend following and breakout detection
- **Trend Following Strategy**: Multi-timeframe trend analysis with pullback entries
- **Strategy Combination**: Intelligent strategy selection based on market conditions
- **Dynamic Parameters**: Self-adjusting strategy parameters based on market volatility

### 📊 **Comprehensive Market Data**
- **Multi-Source Data**: Yahoo Finance with fallback sources
- **Real-Time Feeds**: Live price data with bid/ask spreads
- **Advanced Indicators**: 20+ technical indicators including custom calculations
- **Multiple Timeframes**: M1, M5, M15, M30, H1, H4, D1, W1, MN1
- **Economic Calendar**: Integration with economic events and news

### 🎯 **Advanced Backtesting**
- **Realistic Simulation**: Includes spreads, slippage, and commission
- **Comprehensive Metrics**: 25+ performance and risk metrics
- **Strategy Optimization**: Parameter optimization with genetic algorithms
- **Risk Analysis**: VaR, CVaR, Sharpe, Sortino, Calmar ratios
- **Detailed Reporting**: Trade-by-trade analysis with visualizations

### 🖥️ **Beautiful CLI Interface**
- **Real-Time Dashboard**: Live market data, signals, and performance
- **Interactive Charts**: ASCII charts with technical indicators
- **Performance Monitoring**: Real-time P&L, win rates, and risk metrics
- **Strategy Status**: Live strategy performance and signal generation
- **Risk Dashboard**: Portfolio risk, correlation, and exposure monitoring

### ⚡ **Advanced Risk Management**
- **Dynamic Position Sizing**: Kelly Criterion and volatility-adjusted sizing
- **Multi-Timeframe Risk**: Risk assessment across different timeframes
- **Correlation Analysis**: Portfolio correlation and diversification metrics
- **Volatility Adjustment**: Dynamic risk adjustment based on market volatility
- **Scenario Analysis**: Monte Carlo simulations and stress testing

## 🚀 Quick Start

### 1. **Installation**
```bash
# Clone the repository
git clone <repository-url>
cd advanced-forex-bot

# Install dependencies
pip install -r requirements.txt

# Or use the setup script
python setup.py
```

### 2. **Configuration**
```bash
# Copy configuration template
cp config.env.example config.env

# Edit with your API keys
nano config.env
```

**Required API Keys:**
- **OpenAI API Key**: For AI analysis (get from https://platform.openai.com/)
- **Optional**: OANDA API for live trading (practice account recommended)

### 3. **Test the System**
```bash
# Run comprehensive tests
python test_advanced_system.py
```

### 4. **Start Trading**

#### **Live Trading Mode** (Demo/Analysis Only)
```bash
python advanced_main.py --mode live
```

#### **Backtesting Mode**
```bash
python advanced_main.py --mode backtest --start-date 2024-01-01 --end-date 2024-01-31
```

#### **Strategy Optimization**
```bash
python advanced_main.py --mode optimize --start-date 2024-01-01 --end-date 2024-01-31 --instruments EUR_USD GBP_USD
```

## 📋 Command Line Options

```bash
python advanced_main.py [OPTIONS]

Options:
  --mode {live,backtest,optimize}  Operation mode (default: live)
  --start-date YYYY-MM-DD         Start date for backtest/optimization
  --end-date YYYY-MM-DD           End date for backtest/optimization
  --instruments PAIR [PAIR ...]   Currency pairs to analyze
  --config FILE                   Configuration file (default: config.env)
```

## 🎛️ Configuration Options

### **Trading Parameters**
```bash
# Currency pairs (comma-separated)
DEFAULT_CURRENCY_PAIR=EUR_USD,GBP_USD,USD_JPY,AUD_USD

# Analysis frequency
ANALYSIS_INTERVAL_MINUTES=5

# Risk management
MAX_RISK_PER_TRADE=2.0          # 2% max risk per trade
MAX_DAILY_LOSS=500              # $500 max daily loss
MAX_OPEN_POSITIONS=5            # Max 5 open positions

# AI Configuration
OPENAI_MODEL=gpt-4              # AI model to use
AI_CONFIDENCE_THRESHOLD=0.7     # Minimum confidence for trades
```

### **Strategy Parameters**
```bash
# Scalping Strategy
SCALPING_RSI_PERIOD=14
SCALPING_EMA_FAST=8
SCALPING_EMA_SLOW=21
SCALPING_MIN_CONFIDENCE=0.7

# Day Trading Strategy
DAYTRADING_TREND_EMA_FAST=12
DAYTRADING_TREND_EMA_SLOW=26
DAYTRADING_MIN_CONFIDENCE=0.65

# Trend Following Strategy
TRENDFOLLOWING_EMA_PERIODS=21,50,100
TRENDFOLLOWING_ADX_THRESHOLD=25
TRENDFOLLOWING_MIN_CONFIDENCE=0.7
```

## 📊 Performance Metrics

The system tracks comprehensive performance metrics:

### **Basic Metrics**
- Total Trades, Win Rate, Profit Factor
- Gross Profit/Loss, Average Win/Loss
- Largest Win/Loss, Average Trade Duration

### **Risk Metrics**
- Maximum Drawdown ($ and %)
- Sharpe Ratio, Sortino Ratio, Calmar Ratio
- Value at Risk (VaR), Conditional VaR (CVaR)
- Kelly Criterion, Expectancy

### **Advanced Analytics**
- Strategy Performance Breakdown
- Market Regime Analysis
- Correlation Matrices
- Monte Carlo Simulations

## 🎯 AI Analysis Models

### **Technical Analyst**
- Chart patterns and formations
- Support/resistance levels
- Indicator divergences
- Multi-timeframe confirmation

### **Quantitative Analyst**
- Statistical analysis and probability
- Risk metrics and volatility
- Momentum and mean reversion
- Correlation analysis

### **Macro Analyst**
- Economic fundamentals
- Central bank policies
- Interest rate differentials
- Global market trends

### **Sentiment Analyst**
- Market psychology indicators
- Positioning data analysis
- Volume and flow analysis
- Contrarian signals

### **Risk Manager**
- Position sizing optimization
- Portfolio risk assessment
- Scenario analysis
- Capital preservation strategies

## 🛡️ Safety Features

- **Demo Mode**: All trading is analysis-only by default
- **Risk Limits**: Multiple layers of risk protection
- **Stop Losses**: Automatic loss protection on every trade
- **Position Limits**: Maximum position and exposure limits
- **Daily Limits**: Daily loss and trade count limits
- **AI Validation**: Only high-confidence signals are executed

## 📈 Backtesting Results

Example backtest results (EUR/USD, 2024):

```
📊 PERFORMANCE SUMMARY:
   Total Trades: 156
   Win Rate: 68.6%
   Total P&L: $2,847.50
   Profit Factor: 2.34

📈 RISK METRICS:
   Max Drawdown: $287.50 (2.9%)
   Sharpe Ratio: 2.18
   Sortino Ratio: 3.42
   Calmar Ratio: 4.67

💰 TRADE STATISTICS:
   Average Win: $67.80
   Average Loss: $28.90
   Largest Win: $234.50
   Largest Loss: $89.20
```

## 🔧 Customization

### **Adding New Strategies**
1. Create strategy class inheriting from `BaseStrategy`
2. Implement required methods: `analyze()`, `get_required_indicators()`
3. Add to strategy list in `advanced_main.py`

### **Custom Indicators**
1. Add calculation to `AdvancedDataProvider._calculate_all_indicators()`
2. Update strategy requirements in `get_required_indicators()`

### **AI Model Customization**
1. Modify prompts in `AdvancedAIAnalyzer`
2. Adjust model weights in ensemble decision
3. Add new analysis models

## 🚨 Important Notes

- **Educational Purpose**: This system is for educational and research purposes
- **Risk Warning**: Forex trading involves significant risk of loss
- **Demo First**: Always test thoroughly with demo accounts
- **API Costs**: OpenAI API usage incurs costs based on usage
- **Market Hours**: Consider forex market hours for optimal performance

## 📚 Documentation

- **[Strategy Guide](docs/strategies.md)**: Detailed strategy explanations
- **[API Reference](docs/api.md)**: Complete API documentation
- **[Configuration Guide](docs/configuration.md)**: Advanced configuration options
- **[Backtesting Guide](docs/backtesting.md)**: Comprehensive backtesting tutorial

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational purposes only. Forex trading involves substantial risk of loss and is not suitable for all investors. Past performance is not indicative of future results. Always consult with a qualified financial advisor before making trading decisions.

---

**Built with ❤️ for the trading community**

# Core dependencies for Advanced Forex Trading Bot with OpenAI
openai>=1.0.0
oandapyV20>=0.6.3
pandas>=1.5.0
numpy>=1.24.0
python-dotenv>=1.0.0
requests>=2.28.0
schedule>=1.2.0
colorama>=0.4.6
tabulate>=0.9.0

# Advanced market data and visualization
yfinance>=0.2.18
plotly>=5.17.0
dash>=2.14.0
rich>=13.7.0
textual>=0.41.0

# Technical analysis and machine learning (ta-lib removed due to installation complexity)
# ta-lib>=0.4.25  # Commented out - requires system-level TA-Lib installation
scikit-learn>=1.3.0
scipy>=1.11.0

# Performance and optimization
numba>=0.58.0
cython>=3.0.0

# CLI and interface
click>=8.1.0
prompt-toolkit>=3.0.39
blessed>=1.20.0

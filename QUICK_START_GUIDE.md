# 🚀 Quick Start Guide - Forex Trading Bot

## Step 1: Setup Environment (5 minutes)

### Install Python Dependencies
```bash
# Run the setup script
python setup.py
```

This will:
- ✅ Check Python version (3.8+ required)
- ✅ Install all required packages
- ✅ Create necessary directories
- ✅ Set up configuration files

## Step 2: Get Your API Keys (10 minutes)

### OANDA API Key (FREE Practice Account)
1. **Go to**: https://www.oanda.com/
2. **Click**: "Open Account" → "Practice Account" 
3. **Sign up** with your email (FREE, no credit card needed)
4. **Verify email** and complete registration
5. **In your dashboard**: Find "Manage API Access" or "API" section
6. **Generate Token**: Create new API token
7. **Copy**: Your API token (starts with letters/numbers)
8. **Note**: Your Account ID (format: 101-001-123456-001)

### OpenAI API Key
1. **Go to**: https://platform.openai.com/
2. **Sign in** or create account
3. **Go to**: API Keys section
4. **Create**: New API key
5. **Copy**: Your API key (starts with sk-...)

## Step 3: Configure the Bot (2 minutes)

### Edit config.env file:
```bash
# Open config.env in any text editor
# Replace the placeholder values with your real API keys:

OANDA_API_KEY=your_actual_oanda_api_key_here
OANDA_ACCOUNT_ID=your_actual_account_id_here
OANDA_ENVIRONMENT=practice

OPENAI_API_KEY=your_actual_openai_api_key_here
```

**⚠️ Important**: 
- Keep `OANDA_ENVIRONMENT=practice` for demo trading
- Never share your API keys with anyone
- The practice account uses virtual money (safe for testing)

## Step 4: Test Everything (3 minutes)

### Run the test script:
```bash
python test_complete_system.py
```

This will verify:
- ✅ API connections work
- ✅ All components function properly
- ✅ Your configuration is correct

**Expected output**: All tests should show ✅ PASSED

## Step 5: Start Trading! (1 minute)

### Run the bot:
```bash
python main.py
```

You should see:
- 🤖 Bot startup banner
- 📊 Your account information
- ⚙️ Trading configuration
- 🚀 "Bot is ready to trade!" message

## What Happens Next?

### The bot will automatically:
1. **Analyze markets** every 15 minutes (configurable)
2. **Use AI** to make trading decisions
3. **Execute trades** when conditions are right
4. **Manage risk** with stop-loss and take-profit
5. **Log everything** for your review

### Monitor Your Bot:
- **Console output**: Real-time status updates
- **Log files**: Detailed logs in `logs/` directory
- **OANDA dashboard**: View trades in your OANDA account

## Safety Features 🛡️

Your bot includes multiple safety features:
- ✅ **Demo account**: Uses virtual money by default
- ✅ **Risk limits**: Maximum 2% risk per trade
- ✅ **Daily limits**: Stops if daily loss exceeds $100
- ✅ **Position limits**: Maximum 3 open positions
- ✅ **Stop losses**: Automatic loss protection
- ✅ **AI validation**: Only trades with high confidence

## Customization Options ⚙️

Edit `config.env` to customize:

```bash
# Trading pairs (comma-separated)
DEFAULT_CURRENCY_PAIR=EUR_USD,GBP_USD,USD_JPY

# Risk management
MAX_RISK_PER_TRADE=2          # 2% max risk per trade
MAX_DAILY_LOSS=100            # $100 max daily loss
MAX_OPEN_POSITIONS=3          # Max 3 positions

# Trading schedule (24-hour format)
TRADING_START_HOUR=8          # Start at 8 AM UTC
TRADING_END_HOUR=18           # Stop at 6 PM UTC

# Position sizing
DEFAULT_TRADE_AMOUNT=1000     # Default position size
STOP_LOSS_PIPS=20            # Default stop loss
TAKE_PROFIT_PIPS=40          # Default take profit
```

## Troubleshooting 🔧

### Common Issues:

**"API key not found"**
- ✅ Check config.env file exists
- ✅ Verify API keys are correct (no extra spaces)
- ✅ Make sure you copied the complete keys

**"Connection failed"**
- ✅ Check internet connection
- ✅ Verify OANDA account is active
- ✅ Confirm API token is valid

**"No trading signals"**
- ✅ Normal behavior - bot waits for good opportunities
- ✅ Check if you're in trading hours (8 AM - 6 PM UTC)
- ✅ AI may recommend HOLD in uncertain markets

**"Tests failing"**
- ✅ Run `python test_oanda_connection.py` first
- ✅ Check all API keys are configured
- ✅ Verify internet connection

### Getting Help:

1. **Check logs**: Look in `logs/` directory for detailed error messages
2. **Test connection**: Run `python test_oanda_connection.py`
3. **Verify setup**: Run `python test_complete_system.py`

## Next Steps 📈

### After successful setup:

1. **Monitor for 24 hours**: Watch how the bot behaves
2. **Review logs**: Check `logs/` directory for trade decisions
3. **Analyze performance**: Review trades in OANDA dashboard
4. **Adjust settings**: Modify `config.env` if needed
5. **Consider live trading**: Only after thorough testing

### Performance Tips:

- **Start conservative**: Use default settings initially
- **Monitor closely**: Watch the first few trades carefully
- **Keep learning**: Review AI reasoning in logs
- **Stay informed**: Follow forex market news
- **Test changes**: Always test configuration changes

## Important Reminders ⚠️

- 🔴 **Start with practice account** (virtual money)
- 🔴 **Never risk more than you can afford to lose**
- 🔴 **Monitor the bot regularly**
- 🔴 **Understand the risks of forex trading**
- 🔴 **Keep your API keys secure**

## Success! 🎉

Your forex trading bot is now ready to:
- ✅ Analyze markets with AI
- ✅ Execute trades automatically
- ✅ Manage risk intelligently
- ✅ Learn and adapt over time

**Happy Trading!** 📈🤖

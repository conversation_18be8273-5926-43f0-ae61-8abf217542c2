# 🚀 Installation Guide - Advanced Forex Trading Bot

This guide will help you install and set up the Advanced Forex Trading Bot, including handling common installation issues.

## 📋 Prerequisites

- **Python 3.8+** (Python 3.9-3.11 recommended)
- **Git** (for cloning the repository)
- **Internet connection** (for downloading packages and market data)

## 🔧 Installation Methods

### Method 1: Quick Installation (Recommended)

```bash
# 1. Navigate to the project directory
cd /path/to/advanced-forex-bot

# 2. Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 3. Install basic requirements (without problematic packages)
pip install -r requirements_basic.txt

# 4. Copy configuration template
cp config.env.example config.env

# 5. Edit configuration with your API keys
nano config.env  # or use your preferred editor
```

### Method 2: Full Installation (with TA-Lib)

If you want the full feature set including TA-Lib indicators:

#### On Ubuntu/Debian:
```bash
# Install system dependencies
sudo apt-get update
sudo apt-get install build-essential wget

# Install TA-Lib C library
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
tar -xzf ta-lib-0.4.0-src.tar.gz
cd ta-lib/
./configure --prefix=/usr
make
sudo make install
cd ..

# Install Python packages
pip install -r requirements.txt
```

#### On macOS:
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install TA-Lib
brew install ta-lib

# Install Python packages
pip install -r requirements.txt
```

#### On Windows:
```bash
# Download pre-compiled TA-Lib wheel from:
# https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib

# Install the downloaded wheel
pip install TA_Lib-0.4.XX-cpXX-cpXXm-win_amd64.whl

# Install other requirements
pip install -r requirements.txt
```

## ⚙️ Configuration

### 1. API Keys Setup

Edit `config.env` with your API keys:

```bash
# Required: OpenAI API Key (get from https://platform.openai.com/)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Optional: OANDA API (for live trading - use practice account)
OANDA_API_KEY=your_oanda_api_key
OANDA_ACCOUNT_ID=your_account_id
OANDA_ENVIRONMENT=practice

# Trading Configuration
DEFAULT_CURRENCY_PAIR=EUR_USD,GBP_USD,USD_JPY
ANALYSIS_INTERVAL_MINUTES=5
LOG_LEVEL=INFO

# Risk Management
MAX_RISK_PER_TRADE=2.0
MAX_DAILY_LOSS=500
MAX_OPEN_POSITIONS=5
```

### 2. Getting OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key to your `config.env` file

**Note**: OpenAI API usage incurs costs. Start with a small credit limit for testing.

## 🧪 Testing the Installation

### Quick Test (No Dependencies)
```bash
python3 quick_test.py
```

### Full System Test
```bash
python3 test_advanced_system.py
```

### Manual Test
```bash
# Test basic imports
python3 -c "import pandas, numpy, requests; print('Basic packages OK')"

# Test OpenAI connection
python3 -c "
import os
from dotenv import load_dotenv
load_dotenv('config.env')
api_key = os.getenv('OPENAI_API_KEY')
print('OpenAI API Key:', 'Found' if api_key else 'Missing')
"
```

## 🚀 Running the Bot

### Live Analysis Mode
```bash
python3 advanced_main.py --mode live
```

### Backtesting Mode
```bash
python3 advanced_main.py --mode backtest --start-date 2024-01-01 --end-date 2024-01-31
```

### Help
```bash
python3 advanced_main.py --help
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. TA-Lib Installation Failed
**Error**: `fatal error: ta-lib/ta_defs.h: No such file or directory`

**Solution**: Use the basic requirements file:
```bash
pip install -r requirements_basic.txt
```

The system will work without TA-Lib using built-in technical indicators.

#### 2. OpenAI Import Error
**Error**: `ModuleNotFoundError: No module named 'openai'`

**Solution**:
```bash
pip install openai>=1.0.0
```

#### 3. Permission Denied (Linux/macOS)
**Error**: `Permission denied` when running scripts

**Solution**:
```bash
chmod +x advanced_main.py test_advanced_system.py quick_test.py
```

#### 4. Virtual Environment Issues
**Error**: Packages not found after installation

**Solution**:
```bash
# Make sure virtual environment is activated
source venv/bin/activate  # Linux/macOS
# or
venv\Scripts\activate     # Windows

# Verify you're in the right environment
which python3
pip list
```

#### 5. API Rate Limits
**Error**: OpenAI API rate limit exceeded

**Solution**:
- Increase the analysis interval in `config.env`
- Use a higher tier OpenAI account
- Implement request throttling

#### 6. Memory Issues
**Error**: Out of memory when processing large datasets

**Solution**:
- Reduce the number of instruments analyzed
- Decrease historical data periods
- Use smaller timeframes for backtesting

## 📦 Alternative Installation (Docker)

If you prefer Docker:

```bash
# Create Dockerfile (basic example)
cat > Dockerfile << EOF
FROM python:3.11-slim

WORKDIR /app
COPY requirements_basic.txt .
RUN pip install -r requirements_basic.txt

COPY . .
CMD ["python3", "advanced_main.py", "--mode", "live"]
EOF

# Build and run
docker build -t forex-bot .
docker run -it --env-file config.env forex-bot
```

## 🔄 Updating the System

```bash
# Pull latest changes (if using git)
git pull origin main

# Update dependencies
pip install -r requirements_basic.txt --upgrade

# Run tests
python3 quick_test.py
```

## 💡 Performance Tips

1. **Use SSD storage** for faster data processing
2. **Allocate sufficient RAM** (minimum 4GB recommended)
3. **Stable internet connection** for real-time data
4. **Monitor API usage** to avoid unexpected costs
5. **Start with demo/analysis mode** before live trading

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs** in the `logs/` directory
2. **Run the test suite** to identify specific problems
3. **Verify your configuration** in `config.env`
4. **Check API key validity** and quotas
5. **Review system requirements** and dependencies

## 📚 Next Steps

After successful installation:

1. **Read the documentation**: `ADVANCED_README.md`
2. **Run backtests** to understand the system
3. **Experiment with parameters** in demo mode
4. **Monitor performance** and adjust settings
5. **Consider live trading** only after thorough testing

---

**⚠️ Important**: This system is for educational purposes. Always test thoroughly with demo accounts before considering any live trading.

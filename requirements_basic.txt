# Basic requirements without problematic packages
# Core dependencies for Advanced Forex Trading Bot with OpenAI
openai>=1.0.0
pandas>=1.5.0
numpy>=1.24.0
python-dotenv>=1.0.0
requests>=2.28.0
colorama>=0.4.6
tabulate>=0.9.0

# Advanced market data and visualization
yfinance>=0.2.18
plotly>=5.17.0
rich>=13.7.0

# Machine learning (without ta-lib)
scikit-learn>=1.3.0
scipy>=1.11.0

# CLI and interface
click>=8.1.0

# Optional: OANDA API (comment out if not using OANDA)
# oandapyV20>=0.6.3

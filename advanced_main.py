#!/usr/bin/env python3
"""
Advanced Forex Trading Bot - Main Application
Professional-grade AI-powered forex trading system with advanced features
"""

import os
import sys
import argparse
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dotenv import load_dotenv

# Import our advanced modules
from src.market_data.data_provider import AdvancedDataProvider, MarketDataConfig
from src.ai_engine.advanced_ai_analyzer import AdvancedAIAnalyzer
from src.strategies.scalping_strategy import ScalpingStrategy
from src.strategies.day_trading_strategy import DayTradingStrategy
from src.strategies.trend_following_strategy import TrendFollowingStrategy
from src.backtesting.backtest_engine import AdvancedBacktester
from src.cli.beautiful_cli import BeautifulCLI

class AdvancedTradingBot:
    """
    Advanced AI-powered forex trading bot
    
    Features:
    - Multiple AI analysis models
    - Advanced trading strategies
    - Real-time market analysis
    - Comprehensive backtesting
    - Beautiful CLI interface
    - Risk management
    - Performance analytics
    """
    
    def __init__(self, config_file: str = "config.env"):
        """Initialize the advanced trading bot"""
        # Load configuration
        load_dotenv(config_file)
        
        # Setup logging
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.data_provider = AdvancedDataProvider()
        self.ai_analyzer = AdvancedAIAnalyzer()
        self.cli = BeautifulCLI()
        
        # Initialize strategies
        self.strategies = {
            'scalping': ScalpingStrategy(),
            'day_trading': DayTradingStrategy(),
            'trend_following': TrendFollowingStrategy()
        }
        
        # Configuration
        self.instruments = os.getenv('DEFAULT_CURRENCY_PAIR', 'EUR_USD,GBP_USD,USD_JPY').split(',')
        self.timeframes = ['M1', 'M5', 'M15', 'H1', 'H4', 'D1']
        self.analysis_interval = int(os.getenv('ANALYSIS_INTERVAL_MINUTES', 5))
        
        # State
        self.is_running = False
        self.last_analysis = {}
        
        self.logger.info("Advanced Trading Bot initialized")
    
    def _setup_logging(self):
        """Setup comprehensive logging"""
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'logs/advanced_bot_{datetime.now().strftime("%Y%m%d")}.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def run_live_trading(self):
        """Run live trading with beautiful CLI"""
        try:
            self.logger.info("🚀 Starting Advanced Forex Trading Bot - Live Mode")
            self.is_running = True
            
            # Start CLI in separate thread
            cli_thread = threading.Thread(target=self.cli.start, daemon=True)
            cli_thread.start()
            
            # Start analysis loop
            analysis_thread = threading.Thread(target=self._analysis_loop, daemon=True)
            analysis_thread.start()
            
            # Keep main thread alive
            try:
                while self.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Shutdown requested by user")
                self.stop()
            
        except Exception as e:
            self.logger.error(f"Error in live trading: {e}")
            raise
    
    def _analysis_loop(self):
        """Main analysis loop"""
        while self.is_running:
            try:
                # Get market data for all instruments
                for instrument in self.instruments:
                    self._analyze_instrument(instrument)
                
                # Update CLI with latest data
                self._update_cli_displays()
                
                # Wait for next analysis
                time.sleep(self.analysis_interval * 60)
                
            except Exception as e:
                self.logger.error(f"Error in analysis loop: {e}")
                time.sleep(30)  # Wait 30 seconds before retrying
    
    def _analyze_instrument(self, instrument: str):
        """Analyze a single instrument"""
        try:
            self.logger.info(f"Analyzing {instrument}...")
            
            # Get multi-timeframe market data
            market_data = self.data_provider.get_multi_timeframe_data(
                instrument, self.timeframes
            )
            
            if not market_data:
                self.logger.warning(f"No market data for {instrument}")
                return
            
            # Get real-time price
            real_time_data = self.data_provider.get_real_time_data([instrument])
            
            # Prepare comprehensive market data
            comprehensive_data = {
                'instrument': instrument,
                'current_price': real_time_data.get(instrument, {}),
                **market_data
            }
            
            # Run AI analysis
            ai_result = self.ai_analyzer.comprehensive_analysis(
                comprehensive_data, self.instruments
            )
            
            # Process strategy signals
            strategy_signals = {}
            for strategy_name, strategy in self.strategies.items():
                try:
                    signal = strategy.analyze(comprehensive_data)
                    strategy_signals[strategy_name] = signal
                    
                    # Add signal to CLI
                    if signal.signal_type != 'HOLD':
                        self.cli.add_signal({
                            'instrument': instrument,
                            'signal_type': signal.signal_type,
                            'confidence': signal.confidence,
                            'strategy': strategy_name,
                            'reasoning': signal.reasoning[:100]
                        })
                    
                except Exception as e:
                    self.logger.error(f"Error in strategy {strategy_name}: {e}")
            
            # Store analysis results
            self.last_analysis[instrument] = {
                'timestamp': datetime.now(),
                'ai_result': ai_result,
                'strategy_signals': strategy_signals,
                'market_data': comprehensive_data
            }
            
            # Update CLI market data
            self.cli.update_market_data({instrument: comprehensive_data})
            
            self.logger.info(f"Analysis completed for {instrument}: "
                           f"AI Signal: {ai_result.primary_signal} "
                           f"(Confidence: {ai_result.confidence:.2f})")
            
        except Exception as e:
            self.logger.error(f"Error analyzing {instrument}: {e}")
    
    def _update_cli_displays(self):
        """Update CLI with latest analysis results"""
        try:
            # Update performance metrics (mock data for now)
            self.cli.update_performance({
                'daily_pnl': 125.50,
                'win_rate': 0.68,
                'total_trades': 24,
                'sharpe_ratio': 1.85
            })
            
            # Update strategy status
            strategy_status = {}
            for name, strategy in self.strategies.items():
                strategy_status[name] = {
                    'active': strategy.is_active,
                    'signals_today': strategy.performance.total_signals,
                    'win_rate': strategy.performance.win_rate
                }
            
            self.cli.update_strategies(strategy_status)
            
            # Update risk metrics
            self.cli.update_risk_metrics({
                'portfolio_risk': 'MEDIUM',
                'volatility': 'LOW',
                'correlation': 'LOW',
                'exposure': 0.45
            })
            
        except Exception as e:
            self.logger.error(f"Error updating CLI: {e}")
    
    def run_backtest(self, start_date: str, end_date: str, instruments: List[str] = None):
        """Run comprehensive backtest"""
        try:
            self.logger.info(f"🔬 Starting backtest from {start_date} to {end_date}")
            
            # Parse dates
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            
            # Use provided instruments or default
            test_instruments = instruments or self.instruments
            
            # Get historical data
            historical_data = {}
            for instrument in test_instruments:
                self.logger.info(f"Fetching historical data for {instrument}...")
                
                data = self.data_provider.get_multi_timeframe_data(
                    instrument, ['H1']  # Use H1 for backtesting
                )
                
                if 'H1' in data:
                    historical_data[instrument] = data['H1']
            
            if not historical_data:
                self.logger.error("No historical data available for backtesting")
                return
            
            # Initialize backtester
            backtester = AdvancedBacktester(
                initial_capital=10000.0,
                commission=0.0001,
                spread=0.0002,
                slippage=0.0001
            )
            
            # Run backtest
            results = backtester.run_backtest(
                historical_data,
                list(self.strategies.values()),
                start_dt,
                end_dt
            )
            
            # Display results
            self._display_backtest_results(results)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error running backtest: {e}")
            raise
    
    def _display_backtest_results(self, results):
        """Display comprehensive backtest results"""
        print("\n" + "="*80)
        print("🎯 BACKTEST RESULTS")
        print("="*80)
        
        print(f"\n📊 PERFORMANCE SUMMARY:")
        print(f"   Total Trades: {results.total_trades}")
        print(f"   Win Rate: {results.win_rate:.1%}")
        print(f"   Total P&L: ${results.total_pnl:,.2f}")
        print(f"   Profit Factor: {results.profit_factor:.2f}")
        
        print(f"\n📈 RISK METRICS:")
        print(f"   Max Drawdown: ${results.max_drawdown:,.2f} ({results.max_drawdown_percent:.1%})")
        print(f"   Sharpe Ratio: {results.sharpe_ratio:.2f}")
        print(f"   Sortino Ratio: {results.sortino_ratio:.2f}")
        print(f"   Calmar Ratio: {results.calmar_ratio:.2f}")
        
        print(f"\n💰 TRADE STATISTICS:")
        print(f"   Average Win: ${results.avg_win:.2f}")
        print(f"   Average Loss: ${results.avg_loss:.2f}")
        print(f"   Largest Win: ${results.largest_win:.2f}")
        print(f"   Largest Loss: ${results.largest_loss:.2f}")
        
        print(f"\n🎲 ADVANCED METRICS:")
        print(f"   Expectancy: ${results.expectancy:.2f}")
        print(f"   Kelly Criterion: {results.kelly_criterion:.2%}")
        print(f"   VaR (95%): {results.var_95:.2%}")
        print(f"   CVaR (95%): {results.cvar_95:.2%}")
        
        if results.strategy_performance:
            print(f"\n⚙️ STRATEGY BREAKDOWN:")
            for strategy, perf in results.strategy_performance.items():
                print(f"   {strategy}:")
                print(f"     Trades: {perf['total_trades']}")
                print(f"     Win Rate: {perf['win_rate']:.1%}")
                print(f"     P&L: ${perf['total_pnl']:,.2f}")
        
        print("\n" + "="*80)
    
    def optimize_strategies(self, instrument: str, start_date: str, end_date: str):
        """Optimize strategy parameters"""
        self.logger.info(f"🔧 Optimizing strategies for {instrument}")
        
        # This would implement parameter optimization
        # For now, just a placeholder
        print(f"\n🔧 Strategy Optimization for {instrument}")
        print("   Optimization feature coming soon...")
        print("   This will test different parameter combinations")
        print("   and find the optimal settings for each strategy.")
    
    def stop(self):
        """Stop the trading bot"""
        self.logger.info("Stopping Advanced Trading Bot...")
        self.is_running = False
        self.cli.stop()

def main():
    """Main application entry point"""
    parser = argparse.ArgumentParser(description="Advanced Forex Trading Bot")
    parser.add_argument('--mode', choices=['live', 'backtest', 'optimize'], 
                       default='live', help='Operation mode')
    parser.add_argument('--start-date', type=str, help='Start date for backtest (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='End date for backtest (YYYY-MM-DD)')
    parser.add_argument('--instruments', nargs='+', help='Instruments to trade/test')
    parser.add_argument('--config', default='config.env', help='Configuration file')
    
    args = parser.parse_args()
    
    try:
        # Initialize bot
        bot = AdvancedTradingBot(args.config)
        
        if args.mode == 'live':
            print("🚀 Starting Live Trading Mode")
            print("   Press Ctrl+C to stop")
            bot.run_live_trading()
            
        elif args.mode == 'backtest':
            if not args.start_date or not args.end_date:
                print("❌ Backtest mode requires --start-date and --end-date")
                sys.exit(1)
            
            print(f"🔬 Starting Backtest Mode: {args.start_date} to {args.end_date}")
            bot.run_backtest(args.start_date, args.end_date, args.instruments)
            
        elif args.mode == 'optimize':
            if not args.start_date or not args.end_date:
                print("❌ Optimize mode requires --start-date and --end-date")
                sys.exit(1)
            
            instruments = args.instruments or ['EUR_USD']
            for instrument in instruments:
                bot.optimize_strategies(instrument, args.start_date, args.end_date)
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

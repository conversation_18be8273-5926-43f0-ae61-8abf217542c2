#!/usr/bin/env python3
"""
Forex Trading Bot with OpenAI Integration
A complete automated forex trading system using OANDA broker and OpenAI for decision making.
"""

import os
import sys
import time
import logging
from datetime import datetime
from dotenv import load_dotenv
from colorama import init, Fore, Style

# Initialize colorama for colored console output
init(autoreset=True)

def setup_logging():
    """Set up logging configuration"""
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_filename = f"logs/forex_bot_{datetime.now().strftime('%Y%m%d')}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)

def print_banner():
    """Print welcome banner"""
    banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    FOREX TRADING BOT                        ║
║                  with OpenAI Integration                    ║
║                                                              ║
║  🤖 AI-Powered Trading Decisions                            ║
║  📈 Real-time Market Analysis                               ║
║  🛡️  Advanced Risk Management                               ║
║  🔄 Automated Trade Execution                               ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
    print(banner)

def check_environment():
    """Check if all required environment variables are set"""
    required_vars = [
        'OANDA_API_KEY',
        'OANDA_ACCOUNT_ID', 
        'OPENAI_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"{Fore.RED}❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        print(f"\n💡 Please copy config.env.example to config.env and fill in your API keys{Style.RESET_ALL}")
        return False
    
    print(f"{Fore.GREEN}✅ All environment variables configured{Style.RESET_ALL}")
    return True

def main():
    """Main function to run the forex trading bot"""
    # Load environment variables
    load_dotenv('config.env')
    
    # Set up logging
    logger = setup_logging()
    
    # Print banner
    print_banner()
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    logger.info("Starting Forex Trading Bot...")
    
    try:
        # Import modules (we'll create these in next steps)
        from src.data_fetcher import DataFetcher
        from src.ai_analyzer import AIAnalyzer
        from src.trade_executor import TradeExecutor
        from src.risk_manager import RiskManager
        from src.bot_controller import BotController
        
        # Initialize components
        logger.info("Initializing trading components...")
        
        data_fetcher = DataFetcher()
        ai_analyzer = AIAnalyzer()
        trade_executor = TradeExecutor()
        risk_manager = RiskManager()
        
        # Create and start bot controller
        bot = BotController(data_fetcher, ai_analyzer, trade_executor, risk_manager)
        
        print(f"{Fore.GREEN}🚀 Bot initialized successfully! Starting trading...{Style.RESET_ALL}")
        
        # Start the trading bot
        bot.start()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
        print(f"\n{Fore.YELLOW}🛑 Bot stopped by user{Style.RESET_ALL}")
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")
        sys.exit(1)

if __name__ == "__main__":
    main()

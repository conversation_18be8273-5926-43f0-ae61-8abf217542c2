# Forex Trading Bot with OpenAI Integration

🤖 An intelligent forex trading bot that uses OpenAI's GPT models to make trading decisions and executes trades through OANDA broker.

## Features

- 🧠 **AI-Powered Decisions**: Uses OpenAI GPT for market analysis and trading signals
- 📈 **Real-time Trading**: Connects to OANDA for live forex data and trade execution
- 🛡️ **Risk Management**: Built-in stop-loss, take-profit, and position sizing
- 📊 **Multiple Currency Pairs**: Trade EUR/USD, GBP/USD, USD/JPY, and more
- 🔄 **Automated Operation**: Runs continuously with configurable trading hours
- 📝 **Comprehensive Logging**: Track all trades and decisions

## Quick Start

### 1. Setup Environment

```bash
# Clone or download the project
# Navigate to the project directory

# Run the setup script
python setup.py
```

### 2. Get API Keys

#### OANDA API Key (Free Practice Account)
1. Go to [OANDA](https://www.oanda.com/)
2. Create a free practice account
3. Go to "Manage API Access" in your account
4. Generate an API token
5. Note your Account ID

#### OpenAI API Key
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an account or sign in
3. Go to API Keys section
4. Create a new API key
5. Copy the key (starts with sk-...)

### 3. Configure the Bot

Edit `config.env` file with your API keys:

```env
# OANDA Configuration
OANDA_API_KEY=your_oanda_api_key_here
OANDA_ACCOUNT_ID=your_account_id_here
OANDA_ENVIRONMENT=practice

# OpenAI Configuration  
OPENAI_API_KEY=your_openai_api_key_here
```

### 4. Run the Bot

```bash
python main.py
```

## Configuration Options

Edit `config.env` to customize:

- **Trading Pairs**: Which currency pairs to trade
- **Risk Settings**: Maximum loss per trade, daily limits
- **Trading Hours**: When the bot should be active
- **Position Sizing**: How much to trade per signal

## Safety Features

- **Demo Mode**: Always starts with practice account
- **Stop Loss**: Automatic loss protection
- **Daily Limits**: Maximum daily loss protection
- **Position Limits**: Maximum number of open trades

## Project Structure

```
forex-trading-bot/
├── main.py              # Main application entry point
├── setup.py             # Environment setup script
├── requirements.txt     # Python dependencies
├── config.env          # Configuration file (you create this)
├── src/                # Source code modules
├── logs/               # Trading logs
└── README.md           # This file
```

## Disclaimer

⚠️ **Important**: This bot is for educational purposes. Forex trading involves significant risk. Always:

1. Start with a practice account
2. Never risk more than you can afford to lose
3. Understand the risks involved
4. Test thoroughly before live trading

## Support

- Check the logs/ directory for detailed operation logs
- All trades and decisions are logged for analysis
- Configuration errors are clearly displayed

## License

This project is for educational purposes only. Use at your own risk.

"""
Trade Executor Mo<PERSON>le
Handles order placement, position management, and trade execution via OANDA
"""

import os
import logging
from typing import Dict, List, Optional
from datetime import datetime
import oandapyV20
import oandapyV20.endpoints.orders as orders
import oandapyV20.endpoints.trades as trades
import oandapyV20.endpoints.positions as positions
import oandapyV20.endpoints.accounts as accounts

class TradeExecutor:
    """Executes trades and manages positions via OANDA API"""
    
    def __init__(self):
        """Initialize the trade executor with OANDA credentials"""
        self.logger = logging.getLogger(__name__)
        
        # Get OANDA credentials
        self.api_key = os.getenv('OANDA_API_KEY')
        self.account_id = os.getenv('OANDA_ACCOUNT_ID')
        self.environment = os.getenv('OANDA_ENVIRONMENT', 'practice')
        
        if not self.api_key or not self.account_id:
            raise ValueError("OANDA API credentials not found in environment variables")
        
        # Initialize OANDA client
        self.client = oandapyV20.API(
            access_token=self.api_key,
            environment=self.environment
        )
        
        self.logger.info(f"Trade Executor initialized for {self.environment} environment")
    
    def place_market_order(self, instrument: str, units: int, stop_loss: Optional[float] = None, 
                          take_profit: Optional[float] = None) -> Dict:
        """
        Place a market order
        
        Args:
            instrument: Currency pair (e.g., 'EUR_USD')
            units: Number of units (positive for buy, negative for sell)
            stop_loss: Stop loss price level
            take_profit: Take profit price level
            
        Returns:
            Dict with order result
        """
        try:
            # Prepare order data
            order_data = {
                "order": {
                    "type": "MARKET",
                    "instrument": instrument,
                    "units": str(units),
                    "timeInForce": "FOK"  # Fill or Kill
                }
            }
            
            # Add stop loss if provided
            if stop_loss:
                order_data["order"]["stopLossOnFill"] = {
                    "price": str(stop_loss)
                }
            
            # Add take profit if provided
            if take_profit:
                order_data["order"]["takeProfitOnFill"] = {
                    "price": str(take_profit)
                }
            
            # Place the order
            request = orders.OrderCreate(self.account_id, data=order_data)
            response = self.client.request(request)
            
            # Parse response
            if 'orderFillTransaction' in response:
                fill_transaction = response['orderFillTransaction']
                
                result = {
                    'success': True,
                    'order_id': fill_transaction['id'],
                    'trade_id': fill_transaction.get('tradeOpened', {}).get('tradeID'),
                    'instrument': instrument,
                    'units': int(fill_transaction['units']),
                    'price': float(fill_transaction['price']),
                    'time': fill_transaction['time'],
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'pl': float(fill_transaction.get('pl', 0)),
                    'response': response
                }
                
                action = "BUY" if units > 0 else "SELL"
                self.logger.info(f"Market order executed: {action} {abs(units)} {instrument} at {result['price']}")
                
                return result
            else:
                # Order was rejected
                reject_transaction = response.get('orderRejectTransaction', {})
                error_msg = reject_transaction.get('rejectReason', 'Unknown error')
                
                self.logger.error(f"Order rejected: {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'response': response
                }
                
        except Exception as e:
            self.logger.error(f"Error placing market order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def close_position(self, instrument: str, units: Optional[str] = "ALL") -> Dict:
        """
        Close a position
        
        Args:
            instrument: Currency pair
            units: Number of units to close or "ALL"
            
        Returns:
            Dict with close result
        """
        try:
            # Prepare close data
            if units == "ALL":
                close_data = {"longUnits": "ALL", "shortUnits": "ALL"}
            else:
                close_data = {"units": str(units)}
            
            request = positions.PositionClose(
                accountID=self.account_id,
                instrument=instrument,
                data=close_data
            )
            
            response = self.client.request(request)
            
            # Parse response
            result = {
                'success': True,
                'instrument': instrument,
                'closed_transactions': [],
                'response': response
            }
            
            # Check for long position close
            if 'longOrderFillTransaction' in response:
                long_fill = response['longOrderFillTransaction']
                result['closed_transactions'].append({
                    'side': 'long',
                    'units': long_fill['units'],
                    'price': float(long_fill['price']),
                    'pl': float(long_fill.get('pl', 0))
                })
            
            # Check for short position close
            if 'shortOrderFillTransaction' in response:
                short_fill = response['shortOrderFillTransaction']
                result['closed_transactions'].append({
                    'side': 'short',
                    'units': short_fill['units'],
                    'price': float(short_fill['price']),
                    'pl': float(short_fill.get('pl', 0))
                })
            
            self.logger.info(f"Position closed for {instrument}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_open_positions(self) -> List[Dict]:
        """
        Get all open positions
        
        Returns:
            List of open positions
        """
        try:
            request = positions.OpenPositions(self.account_id)
            response = self.client.request(request)
            
            open_positions = []
            for position in response['positions']:
                if float(position['long']['units']) != 0 or float(position['short']['units']) != 0:
                    pos_data = {
                        'instrument': position['instrument'],
                        'long_units': float(position['long']['units']),
                        'long_avg_price': float(position['long']['averagePrice']) if position['long']['averagePrice'] else 0,
                        'long_pl': float(position['long']['unrealizedPL']),
                        'short_units': float(position['short']['units']),
                        'short_avg_price': float(position['short']['averagePrice']) if position['short']['averagePrice'] else 0,
                        'short_pl': float(position['short']['unrealizedPL']),
                        'total_pl': float(position['unrealizedPL'])
                    }
                    open_positions.append(pos_data)
            
            return open_positions
            
        except Exception as e:
            self.logger.error(f"Error getting open positions: {e}")
            return []
    
    def get_open_trades(self) -> List[Dict]:
        """
        Get all open trades
        
        Returns:
            List of open trades
        """
        try:
            request = trades.OpenTrades(self.account_id)
            response = self.client.request(request)
            
            open_trades = []
            for trade in response['trades']:
                trade_data = {
                    'trade_id': trade['id'],
                    'instrument': trade['instrument'],
                    'units': float(trade['currentUnits']),
                    'price': float(trade['price']),
                    'unrealized_pl': float(trade['unrealizedPL']),
                    'open_time': trade['openTime'],
                    'stop_loss': float(trade['stopLossOrder']['price']) if trade.get('stopLossOrder') else None,
                    'take_profit': float(trade['takeProfitOrder']['price']) if trade.get('takeProfitOrder') else None
                }
                open_trades.append(trade_data)
            
            return open_trades
            
        except Exception as e:
            self.logger.error(f"Error getting open trades: {e}")
            return []
    
    def modify_trade(self, trade_id: str, stop_loss: Optional[float] = None, 
                    take_profit: Optional[float] = None) -> Dict:
        """
        Modify stop loss and/or take profit for an existing trade
        
        Args:
            trade_id: Trade ID to modify
            stop_loss: New stop loss price
            take_profit: New take profit price
            
        Returns:
            Dict with modification result
        """
        try:
            # Get current trade details
            request = trades.TradeDetails(self.account_id, trade_id)
            response = self.client.request(request)
            trade = response['trade']
            
            modifications = []
            
            # Modify stop loss
            if stop_loss is not None:
                if trade.get('stopLossOrder'):
                    # Modify existing stop loss
                    sl_order_id = trade['stopLossOrder']['id']
                    sl_data = {
                        "order": {
                            "type": "STOP_LOSS",
                            "tradeID": trade_id,
                            "price": str(stop_loss)
                        }
                    }
                    sl_request = orders.OrderReplace(self.account_id, sl_order_id, data=sl_data)
                    sl_response = self.client.request(sl_request)
                    modifications.append(('stop_loss', sl_response))
                else:
                    # Create new stop loss
                    sl_data = {
                        "order": {
                            "type": "STOP_LOSS",
                            "tradeID": trade_id,
                            "price": str(stop_loss)
                        }
                    }
                    sl_request = orders.OrderCreate(self.account_id, data=sl_data)
                    sl_response = self.client.request(sl_request)
                    modifications.append(('stop_loss', sl_response))
            
            # Modify take profit
            if take_profit is not None:
                if trade.get('takeProfitOrder'):
                    # Modify existing take profit
                    tp_order_id = trade['takeProfitOrder']['id']
                    tp_data = {
                        "order": {
                            "type": "TAKE_PROFIT",
                            "tradeID": trade_id,
                            "price": str(take_profit)
                        }
                    }
                    tp_request = orders.OrderReplace(self.account_id, tp_order_id, data=tp_data)
                    tp_response = self.client.request(tp_request)
                    modifications.append(('take_profit', tp_response))
                else:
                    # Create new take profit
                    tp_data = {
                        "order": {
                            "type": "TAKE_PROFIT",
                            "tradeID": trade_id,
                            "price": str(take_profit)
                        }
                    }
                    tp_request = orders.OrderCreate(self.account_id, data=tp_data)
                    tp_response = self.client.request(tp_request)
                    modifications.append(('take_profit', tp_response))
            
            self.logger.info(f"Trade {trade_id} modified successfully")
            return {
                'success': True,
                'trade_id': trade_id,
                'modifications': modifications
            }
            
        except Exception as e:
            self.logger.error(f"Error modifying trade {trade_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def close_trade(self, trade_id: str, units: Optional[str] = None) -> Dict:
        """
        Close a specific trade
        
        Args:
            trade_id: Trade ID to close
            units: Number of units to close (None for all)
            
        Returns:
            Dict with close result
        """
        try:
            close_data = {}
            if units:
                close_data["units"] = str(units)
            
            request = trades.TradeClose(
                accountID=self.account_id,
                tradeID=trade_id,
                data=close_data if close_data else None
            )
            
            response = self.client.request(request)
            
            if 'orderFillTransaction' in response:
                fill_transaction = response['orderFillTransaction']
                
                result = {
                    'success': True,
                    'trade_id': trade_id,
                    'units_closed': int(fill_transaction['units']),
                    'price': float(fill_transaction['price']),
                    'pl': float(fill_transaction.get('pl', 0)),
                    'time': fill_transaction['time']
                }
                
                self.logger.info(f"Trade {trade_id} closed: P&L = {result['pl']}")
                return result
            else:
                return {
                    'success': False,
                    'error': 'No fill transaction in response'
                }
                
        except Exception as e:
            self.logger.error(f"Error closing trade {trade_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_account_summary(self) -> Dict:
        """
        Get account summary information
        
        Returns:
            Dict with account details
        """
        try:
            request = accounts.AccountSummary(self.account_id)
            response = self.client.request(request)
            
            account = response['account']
            
            summary = {
                'account_id': account['id'],
                'currency': account['currency'],
                'balance': float(account['balance']),
                'nav': float(account['NAV']),
                'unrealized_pl': float(account['unrealizedPL']),
                'realized_pl': float(account['pl']),
                'margin_used': float(account['marginUsed']),
                'margin_available': float(account['marginAvailable']),
                'open_trade_count': int(account['openTradeCount']),
                'open_position_count': int(account['openPositionCount']),
                'last_transaction_id': account['lastTransactionID']
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error getting account summary: {e}")
            return {}

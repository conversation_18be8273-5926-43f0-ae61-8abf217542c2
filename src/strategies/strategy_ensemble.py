"""
Strategy Ensemble System
Advanced system for combining multiple trading strategies using various ensemble methods
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from enum import Enum
import logging
from dataclasses import dataclass, field

from .base_strategy import BaseStrategy, Signal, StrategyPerformance

class EnsembleMethod(Enum):
    """Ensemble combination methods"""
    VOTING = "voting"                    # Simple majority voting
    WEIGHTED_VOTING = "weighted_voting"  # Weighted by confidence
    CONFIDENCE_WEIGHTED = "confidence_weighted"  # Weighted by historical performance
    PERFORMANCE_WEIGHTED = "performance_weighted"  # Weighted by recent performance
    ADAPTIVE = "adaptive"                # Dynamically adjust weights
    CONSENSUS = "consensus"              # Require consensus above threshold

@dataclass
class EnsembleSignal:
    """Enhanced signal with ensemble information"""
    signal: Signal
    individual_signals: List[Tuple[str, Signal]]  # (strategy_name, signal)
    ensemble_method: EnsembleMethod
    strategy_weights: Dict[str, float]
    consensus_level: float
    confidence_distribution: Dict[str, float]

@dataclass
class StrategyWeight:
    """Dynamic strategy weight with performance tracking"""
    current_weight: float = 1.0
    performance_score: float = 0.0
    recent_accuracy: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)

class StrategyEnsemble(BaseStrategy):
    """
    Advanced strategy ensemble system
    
    Features:
    - Multiple ensemble methods
    - Dynamic weight adjustment
    - Performance-based strategy selection
    - Consensus-based filtering
    - Real-time strategy ranking
    - Adaptive learning
    """
    
    def __init__(self, strategies: List[BaseStrategy], 
                 ensemble_method: EnsembleMethod = EnsembleMethod.CONFIDENCE_WEIGHTED,
                 parameters: Dict = None):
        """
        Initialize strategy ensemble
        
        Args:
            strategies: List of strategy instances
            ensemble_method: Method for combining strategies
            parameters: Ensemble-specific parameters
        """
        default_params = {
            'consensus_threshold': 0.6,      # Minimum consensus for signal
            'min_strategies_agree': 2,       # Minimum strategies that must agree
            'weight_decay': 0.95,           # Weight decay for adaptive learning
            'performance_window': 50,        # Window for performance calculation
            'rebalance_frequency': 10,       # How often to rebalance weights
            'min_confidence': 0.65,         # Minimum ensemble confidence
            'max_strategy_weight': 0.5,     # Maximum weight for single strategy
            'outlier_threshold': 2.0,       # Standard deviations for outlier detection
        }
        
        if parameters:
            default_params.update(parameters)
        
        # Combine timeframes from all strategies
        all_timeframes = set()
        for strategy in strategies:
            all_timeframes.update(strategy.timeframes)
        
        super().__init__(
            name="StrategyEnsemble",
            timeframes=list(all_timeframes),
            parameters=default_params
        )
        
        self.strategies = {strategy.name: strategy for strategy in strategies}
        self.ensemble_method = ensemble_method
        self.strategy_weights = {name: StrategyWeight() for name in self.strategies.keys()}
        
        # Performance tracking
        self.signal_history = []
        self.performance_history = []
        self.rebalance_counter = 0
        
        # Initialize equal weights
        initial_weight = 1.0 / len(self.strategies)
        for weight in self.strategy_weights.values():
            weight.current_weight = initial_weight
        
        self.logger.info(f"Ensemble initialized with {len(strategies)} strategies using {ensemble_method.value}")
    
    def analyze(self, market_data: Dict[str, pd.DataFrame], 
                timestamp: datetime = None) -> Signal:
        """
        Generate ensemble signal by combining individual strategy signals
        
        Args:
            market_data: Market data for all timeframes
            timestamp: Current timestamp
            
        Returns:
            Combined ensemble signal
        """
        try:
            if timestamp is None:
                timestamp = datetime.now()
            
            # Get signals from all strategies
            individual_signals = []
            valid_strategies = []
            
            for strategy_name, strategy in self.strategies.items():
                try:
                    # Get strategy-specific data
                    strategy_data = self._prepare_strategy_data(market_data, strategy.timeframes)
                    
                    # Generate signal
                    signal = strategy.analyze(strategy_data, timestamp)
                    
                    if signal and signal.confidence > 0.1:  # Filter out very low confidence signals
                        individual_signals.append((strategy_name, signal))
                        valid_strategies.append(strategy_name)
                        
                except Exception as e:
                    self.logger.warning(f"Strategy {strategy_name} failed: {e}")
                    continue
            
            if len(individual_signals) < 2:
                return self._create_hold_signal(market_data, "Insufficient valid strategy signals")
            
            # Combine signals using selected ensemble method
            ensemble_signal = self._combine_signals(individual_signals, timestamp)
            
            # Update performance tracking
            self._update_signal_history(ensemble_signal, individual_signals)
            
            # Rebalance weights if needed
            if self.rebalance_counter >= self.parameters['rebalance_frequency']:
                self._rebalance_weights()
                self.rebalance_counter = 0
            else:
                self.rebalance_counter += 1
            
            return ensemble_signal.signal
            
        except Exception as e:
            self.logger.error(f"Ensemble analysis error: {e}")
            return self._create_hold_signal(market_data, f"Ensemble error: {str(e)}")
    
    def _prepare_strategy_data(self, market_data: Dict[str, pd.DataFrame], 
                              timeframes: List[str]) -> Dict[str, pd.DataFrame]:
        """Prepare data for specific strategy timeframes"""
        strategy_data = {}
        for tf in timeframes:
            if tf in market_data:
                strategy_data[tf] = market_data[tf]
        return strategy_data
    
    def _combine_signals(self, individual_signals: List[Tuple[str, Signal]], 
                        timestamp: datetime) -> EnsembleSignal:
        """Combine individual signals using the selected ensemble method"""
        
        if self.ensemble_method == EnsembleMethod.VOTING:
            return self._voting_ensemble(individual_signals, timestamp)
        elif self.ensemble_method == EnsembleMethod.WEIGHTED_VOTING:
            return self._weighted_voting_ensemble(individual_signals, timestamp)
        elif self.ensemble_method == EnsembleMethod.CONFIDENCE_WEIGHTED:
            return self._confidence_weighted_ensemble(individual_signals, timestamp)
        elif self.ensemble_method == EnsembleMethod.PERFORMANCE_WEIGHTED:
            return self._performance_weighted_ensemble(individual_signals, timestamp)
        elif self.ensemble_method == EnsembleMethod.ADAPTIVE:
            return self._adaptive_ensemble(individual_signals, timestamp)
        elif self.ensemble_method == EnsembleMethod.CONSENSUS:
            return self._consensus_ensemble(individual_signals, timestamp)
        else:
            return self._confidence_weighted_ensemble(individual_signals, timestamp)
    
    def _voting_ensemble(self, individual_signals: List[Tuple[str, Signal]], 
                        timestamp: datetime) -> EnsembleSignal:
        """Simple majority voting ensemble"""
        buy_votes = sum(1 for _, signal in individual_signals if signal.signal_type == 'BUY')
        sell_votes = sum(1 for _, signal in individual_signals if signal.signal_type == 'SELL')
        hold_votes = len(individual_signals) - buy_votes - sell_votes
        
        total_votes = len(individual_signals)
        
        if buy_votes > sell_votes and buy_votes > hold_votes:
            signal_type = 'BUY'
            confidence = buy_votes / total_votes
        elif sell_votes > buy_votes and sell_votes > hold_votes:
            signal_type = 'SELL'
            confidence = sell_votes / total_votes
        else:
            signal_type = 'HOLD'
            confidence = max(buy_votes, sell_votes, hold_votes) / total_votes
        
        # Calculate average prices from agreeing strategies
        agreeing_signals = [signal for _, signal in individual_signals 
                          if signal.signal_type == signal_type]
        
        if agreeing_signals:
            entry_price = np.mean([s.entry_price for s in agreeing_signals if s.entry_price])
            stop_loss = np.mean([s.stop_loss for s in agreeing_signals if s.stop_loss])
            take_profit = np.mean([s.take_profit for s in agreeing_signals if s.take_profit])
        else:
            entry_price = stop_loss = take_profit = None
        
        ensemble_signal = Signal(
            signal_type=signal_type,
            confidence=confidence,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timestamp=timestamp,
            reasoning=f"Voting ensemble: {signal_type} ({buy_votes}B/{sell_votes}S/{hold_votes}H)"
        )
        
        return EnsembleSignal(
            signal=ensemble_signal,
            individual_signals=individual_signals,
            ensemble_method=EnsembleMethod.VOTING,
            strategy_weights={name: 1.0 for name, _ in individual_signals},
            consensus_level=confidence,
            confidence_distribution={name: signal.confidence for name, signal in individual_signals}
        )

    def _weighted_voting_ensemble(self, individual_signals: List[Tuple[str, Signal]],
                                 timestamp: datetime) -> EnsembleSignal:
        """Weighted voting ensemble based on strategy weights"""
        buy_weight = 0
        sell_weight = 0
        hold_weight = 0
        total_weight = 0

        strategy_weights = {}

        for strategy_name, signal in individual_signals:
            weight = self.strategy_weights[strategy_name].current_weight
            strategy_weights[strategy_name] = weight
            total_weight += weight

            if signal.signal_type == 'BUY':
                buy_weight += weight
            elif signal.signal_type == 'SELL':
                sell_weight += weight
            else:
                hold_weight += weight

        if total_weight == 0:
            signal_type = 'HOLD'
            confidence = 0.0
        elif buy_weight > sell_weight and buy_weight > hold_weight:
            signal_type = 'BUY'
            confidence = buy_weight / total_weight
        elif sell_weight > buy_weight and sell_weight > hold_weight:
            signal_type = 'SELL'
            confidence = sell_weight / total_weight
        else:
            signal_type = 'HOLD'
            confidence = max(buy_weight, sell_weight, hold_weight) / total_weight

        # Calculate weighted average prices from agreeing strategies
        agreeing_signals = [(name, signal) for name, signal in individual_signals
                          if signal.signal_type == signal_type]

        if agreeing_signals:
            total_agreeing_weight = sum(self.strategy_weights[name].current_weight
                                      for name, _ in agreeing_signals)

            entry_prices = []
            stop_losses = []
            take_profits = []

            for name, signal in agreeing_signals:
                weight = self.strategy_weights[name].current_weight
                if signal.entry_price:
                    entry_prices.append(signal.entry_price * weight)
                if signal.stop_loss:
                    stop_losses.append(signal.stop_loss * weight)
                if signal.take_profit:
                    take_profits.append(signal.take_profit * weight)

            entry_price = sum(entry_prices) / total_agreeing_weight if entry_prices else None
            stop_loss = sum(stop_losses) / total_agreeing_weight if stop_losses else None
            take_profit = sum(take_profits) / total_agreeing_weight if take_profits else None
        else:
            entry_price = stop_loss = take_profit = None

        ensemble_signal = Signal(
            signal_type=signal_type,
            confidence=confidence,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timestamp=timestamp,
            reasoning=f"Weighted voting ensemble: {signal_type} (weight: {confidence:.3f})"
        )

        return EnsembleSignal(
            signal=ensemble_signal,
            individual_signals=individual_signals,
            ensemble_method=EnsembleMethod.WEIGHTED_VOTING,
            strategy_weights=strategy_weights,
            consensus_level=confidence,
            confidence_distribution={name: signal.confidence for name, signal in individual_signals}
        )

    def _confidence_weighted_ensemble(self, individual_signals: List[Tuple[str, Signal]],
                                    timestamp: datetime) -> EnsembleSignal:
        """Confidence-weighted ensemble based on signal confidence"""
        buy_weight = 0
        sell_weight = 0
        total_weight = 0

        weighted_prices = {'entry': [], 'stop': [], 'take_profit': []}
        weights_for_prices = {'entry': [], 'stop': [], 'take_profit': []}

        for strategy_name, signal in individual_signals:
            weight = signal.confidence
            total_weight += weight

            if signal.signal_type == 'BUY':
                buy_weight += weight
                if signal.entry_price:
                    weighted_prices['entry'].append(signal.entry_price * weight)
                    weights_for_prices['entry'].append(weight)
                if signal.stop_loss:
                    weighted_prices['stop'].append(signal.stop_loss * weight)
                    weights_for_prices['stop'].append(weight)
                if signal.take_profit:
                    weighted_prices['take_profit'].append(signal.take_profit * weight)
                    weights_for_prices['take_profit'].append(weight)

            elif signal.signal_type == 'SELL':
                sell_weight += weight
                if signal.entry_price:
                    weighted_prices['entry'].append(signal.entry_price * weight)
                    weights_for_prices['entry'].append(weight)
                if signal.stop_loss:
                    weighted_prices['stop'].append(signal.stop_loss * weight)
                    weights_for_prices['stop'].append(weight)
                if signal.take_profit:
                    weighted_prices['take_profit'].append(signal.take_profit * weight)
                    weights_for_prices['take_profit'].append(weight)

        if total_weight == 0:
            signal_type = 'HOLD'
            confidence = 0.0
        elif buy_weight > sell_weight:
            signal_type = 'BUY'
            confidence = buy_weight / total_weight
        elif sell_weight > buy_weight:
            signal_type = 'SELL'
            confidence = sell_weight / total_weight
        else:
            signal_type = 'HOLD'
            confidence = max(buy_weight, sell_weight) / total_weight

        # Calculate weighted average prices
        entry_price = (sum(weighted_prices['entry']) / sum(weights_for_prices['entry'])
                      if weights_for_prices['entry'] else None)
        stop_loss = (sum(weighted_prices['stop']) / sum(weights_for_prices['stop'])
                    if weights_for_prices['stop'] else None)
        take_profit = (sum(weighted_prices['take_profit']) / sum(weights_for_prices['take_profit'])
                      if weights_for_prices['take_profit'] else None)

        ensemble_signal = Signal(
            signal_type=signal_type,
            confidence=confidence,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timestamp=timestamp,
            reasoning=f"Confidence-weighted ensemble: {signal_type} (conf: {confidence:.3f})"
        )

        return EnsembleSignal(
            signal=ensemble_signal,
            individual_signals=individual_signals,
            ensemble_method=EnsembleMethod.CONFIDENCE_WEIGHTED,
            strategy_weights={name: signal.confidence for name, signal in individual_signals},
            consensus_level=confidence,
            confidence_distribution={name: signal.confidence for name, signal in individual_signals}
        )

    def _performance_weighted_ensemble(self, individual_signals: List[Tuple[str, Signal]],
                                     timestamp: datetime) -> EnsembleSignal:
        """Performance-weighted ensemble based on historical strategy performance"""
        buy_weight = 0
        sell_weight = 0
        total_weight = 0

        strategy_weights = {}
        weighted_prices = {'entry': [], 'stop': [], 'take_profit': []}
        weights_for_prices = {'entry': [], 'stop': [], 'take_profit': []}

        for strategy_name, signal in individual_signals:
            # Get performance-based weight
            perf_weight = self.strategy_weights[strategy_name].current_weight
            signal_weight = signal.confidence * perf_weight
            strategy_weights[strategy_name] = perf_weight

            total_weight += signal_weight

            if signal.signal_type == 'BUY':
                buy_weight += signal_weight
                self._add_weighted_prices(signal, signal_weight, weighted_prices, weights_for_prices)
            elif signal.signal_type == 'SELL':
                sell_weight += signal_weight
                self._add_weighted_prices(signal, signal_weight, weighted_prices, weights_for_prices)

        if total_weight == 0:
            signal_type = 'HOLD'
            confidence = 0.0
        elif buy_weight > sell_weight:
            signal_type = 'BUY'
            confidence = min(buy_weight / total_weight, 1.0)
        elif sell_weight > buy_weight:
            signal_type = 'SELL'
            confidence = min(sell_weight / total_weight, 1.0)
        else:
            signal_type = 'HOLD'
            confidence = max(buy_weight, sell_weight) / total_weight if total_weight > 0 else 0.0

        # Calculate weighted average prices
        entry_price = self._calculate_weighted_price(weighted_prices['entry'], weights_for_prices['entry'])
        stop_loss = self._calculate_weighted_price(weighted_prices['stop'], weights_for_prices['stop'])
        take_profit = self._calculate_weighted_price(weighted_prices['take_profit'], weights_for_prices['take_profit'])

        ensemble_signal = Signal(
            signal_type=signal_type,
            confidence=confidence,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timestamp=timestamp,
            reasoning=f"Performance-weighted ensemble: {signal_type} (conf: {confidence:.3f})"
        )

        return EnsembleSignal(
            signal=ensemble_signal,
            individual_signals=individual_signals,
            ensemble_method=EnsembleMethod.PERFORMANCE_WEIGHTED,
            strategy_weights=strategy_weights,
            consensus_level=confidence,
            confidence_distribution={name: signal.confidence for name, signal in individual_signals}
        )

    def _adaptive_ensemble(self, individual_signals: List[Tuple[str, Signal]],
                          timestamp: datetime) -> EnsembleSignal:
        """Adaptive ensemble that adjusts weights based on recent performance"""
        # Update weights based on recent performance
        self._update_adaptive_weights()

        # Use performance-weighted method with updated weights
        return self._performance_weighted_ensemble(individual_signals, timestamp)

    def _consensus_ensemble(self, individual_signals: List[Tuple[str, Signal]],
                           timestamp: datetime) -> EnsembleSignal:
        """Consensus ensemble requiring agreement above threshold"""
        threshold = self.parameters['consensus_threshold']
        min_strategies = self.parameters['min_strategies_agree']

        # Count signals by type
        signal_counts = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
        signal_confidences = {'BUY': [], 'SELL': [], 'HOLD': []}

        for strategy_name, signal in individual_signals:
            signal_counts[signal.signal_type] += 1
            signal_confidences[signal.signal_type].append(signal.confidence)

        total_signals = len(individual_signals)

        # Check for consensus
        for signal_type in ['BUY', 'SELL']:
            count = signal_counts[signal_type]
            if count >= min_strategies and count / total_signals >= threshold:
                # Calculate consensus confidence
                avg_confidence = np.mean(signal_confidences[signal_type])
                consensus_strength = count / total_signals
                final_confidence = avg_confidence * consensus_strength

                # Get prices from agreeing strategies
                agreeing_signals = [signal for _, signal in individual_signals
                                  if signal.signal_type == signal_type]

                entry_price = np.mean([s.entry_price for s in agreeing_signals if s.entry_price])
                stop_loss = np.mean([s.stop_loss for s in agreeing_signals if s.stop_loss])
                take_profit = np.mean([s.take_profit for s in agreeing_signals if s.take_profit])

                ensemble_signal = Signal(
                    signal_type=signal_type,
                    confidence=final_confidence,
                    entry_price=entry_price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    timestamp=timestamp,
                    reasoning=f"Consensus ensemble: {signal_type} ({count}/{total_signals} agree, {consensus_strength:.2f})"
                )

                return EnsembleSignal(
                    signal=ensemble_signal,
                    individual_signals=individual_signals,
                    ensemble_method=EnsembleMethod.CONSENSUS,
                    strategy_weights={name: 1.0 for name, _ in individual_signals},
                    consensus_level=consensus_strength,
                    confidence_distribution={name: signal.confidence for name, signal in individual_signals}
                )

        # No consensus reached
        ensemble_signal = Signal(
            signal_type='HOLD',
            confidence=0.0,
            entry_price=None,
            stop_loss=None,
            take_profit=None,
            timestamp=timestamp,
            reasoning=f"No consensus reached (threshold: {threshold})"
        )

        return EnsembleSignal(
            signal=ensemble_signal,
            individual_signals=individual_signals,
            ensemble_method=EnsembleMethod.CONSENSUS,
            strategy_weights={name: 1.0 for name, _ in individual_signals},
            consensus_level=0.0,
            confidence_distribution={name: signal.confidence for name, signal in individual_signals}
        )

    def _add_weighted_prices(self, signal: Signal, weight: float,
                           weighted_prices: Dict, weights_for_prices: Dict):
        """Helper to add weighted prices"""
        if signal.entry_price:
            weighted_prices['entry'].append(signal.entry_price * weight)
            weights_for_prices['entry'].append(weight)
        if signal.stop_loss:
            weighted_prices['stop'].append(signal.stop_loss * weight)
            weights_for_prices['stop'].append(weight)
        if signal.take_profit:
            weighted_prices['take_profit'].append(signal.take_profit * weight)
            weights_for_prices['take_profit'].append(weight)

    def _calculate_weighted_price(self, weighted_prices: List[float],
                                weights: List[float]) -> Optional[float]:
        """Calculate weighted average price"""
        if not weights:
            return None
        return sum(weighted_prices) / sum(weights)

    def _update_signal_history(self, ensemble_signal: EnsembleSignal,
                             individual_signals: List[Tuple[str, Signal]]):
        """Update signal history for performance tracking"""
        self.signal_history.append({
            'timestamp': ensemble_signal.signal.timestamp,
            'ensemble_signal': ensemble_signal.signal,
            'individual_signals': individual_signals,
            'method': ensemble_signal.ensemble_method,
            'consensus': ensemble_signal.consensus_level
        })

        # Keep only recent history
        max_history = self.parameters['performance_window'] * 2
        if len(self.signal_history) > max_history:
            self.signal_history = self.signal_history[-max_history:]

    def _rebalance_weights(self):
        """Rebalance strategy weights based on recent performance"""
        try:
            if len(self.signal_history) < 10:  # Need minimum history
                return

            # Calculate performance metrics for each strategy
            strategy_performance = {}

            for strategy_name in self.strategies.keys():
                performance = self._calculate_strategy_performance(strategy_name)
                strategy_performance[strategy_name] = performance

            # Update weights based on performance
            total_score = sum(perf['score'] for perf in strategy_performance.values())

            if total_score > 0:
                for strategy_name, performance in strategy_performance.items():
                    new_weight = performance['score'] / total_score

                    # Apply constraints
                    max_weight = self.parameters['max_strategy_weight']
                    new_weight = min(new_weight, max_weight)

                    # Update with decay
                    decay = self.parameters['weight_decay']
                    current_weight = self.strategy_weights[strategy_name].current_weight
                    self.strategy_weights[strategy_name].current_weight = (
                        decay * current_weight + (1 - decay) * new_weight
                    )

                    # Update performance metrics
                    self.strategy_weights[strategy_name].performance_score = performance['score']
                    self.strategy_weights[strategy_name].recent_accuracy = performance['accuracy']
                    self.strategy_weights[strategy_name].last_updated = datetime.now()

            # Normalize weights
            self._normalize_weights()

            self.logger.info("Strategy weights rebalanced")

        except Exception as e:
            self.logger.error(f"Error rebalancing weights: {e}")

    def _calculate_strategy_performance(self, strategy_name: str) -> Dict:
        """Calculate recent performance metrics for a strategy"""
        recent_signals = self.signal_history[-self.parameters['performance_window']:]

        correct_predictions = 0
        total_predictions = 0
        confidence_sum = 0

        for signal_data in recent_signals:
            individual_signals = signal_data['individual_signals']

            # Find this strategy's signal
            strategy_signal = None
            for name, signal in individual_signals:
                if name == strategy_name:
                    strategy_signal = signal
                    break

            if strategy_signal and strategy_signal.signal_type != 'HOLD':
                total_predictions += 1
                confidence_sum += strategy_signal.confidence

                # Simple accuracy check (this would be enhanced with actual trade results)
                ensemble_signal = signal_data['ensemble_signal']
                if strategy_signal.signal_type == ensemble_signal.signal_type:
                    correct_predictions += 1

        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.5
        avg_confidence = confidence_sum / total_predictions if total_predictions > 0 else 0.5

        # Combine accuracy and confidence for overall score
        score = (accuracy * 0.7 + avg_confidence * 0.3)

        return {
            'score': score,
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'total_predictions': total_predictions
        }

    def _normalize_weights(self):
        """Normalize strategy weights to sum to 1"""
        total_weight = sum(w.current_weight for w in self.strategy_weights.values())

        if total_weight > 0:
            for weight in self.strategy_weights.values():
                weight.current_weight /= total_weight

    def _update_adaptive_weights(self):
        """Update weights for adaptive ensemble"""
        # This is called more frequently than rebalance_weights
        if len(self.signal_history) < 5:
            return

        # Quick performance update based on recent signals
        recent_signals = self.signal_history[-5:]

        for strategy_name in self.strategies.keys():
            recent_accuracy = 0
            count = 0

            for signal_data in recent_signals:
                individual_signals = signal_data['individual_signals']

                for name, signal in individual_signals:
                    if name == strategy_name and signal.signal_type != 'HOLD':
                        count += 1
                        ensemble_signal = signal_data['ensemble_signal']
                        if signal.signal_type == ensemble_signal.signal_type:
                            recent_accuracy += 1

            if count > 0:
                accuracy = recent_accuracy / count
                current_weight = self.strategy_weights[strategy_name].current_weight

                # Adjust weight based on recent accuracy
                adjustment = (accuracy - 0.5) * 0.1  # Small adjustment
                new_weight = max(0.1, min(0.9, current_weight + adjustment))

                self.strategy_weights[strategy_name].current_weight = new_weight

        # Normalize after adjustment
        self._normalize_weights()

    def get_strategy_rankings(self) -> List[Tuple[str, Dict]]:
        """Get current strategy rankings by performance"""
        rankings = []

        for strategy_name, weight_info in self.strategy_weights.items():
            rankings.append((strategy_name, {
                'weight': weight_info.current_weight,
                'performance_score': weight_info.performance_score,
                'recent_accuracy': weight_info.recent_accuracy,
                'sharpe_ratio': weight_info.sharpe_ratio,
                'max_drawdown': weight_info.max_drawdown
            }))

        # Sort by performance score
        rankings.sort(key=lambda x: x[1]['performance_score'], reverse=True)
        return rankings

    def get_ensemble_stats(self) -> Dict:
        """Get comprehensive ensemble statistics"""
        if not self.signal_history:
            return {}

        recent_signals = self.signal_history[-50:]  # Last 50 signals

        # Signal distribution
        signal_types = [s['ensemble_signal'].signal_type for s in recent_signals]
        signal_distribution = {
            'BUY': signal_types.count('BUY'),
            'SELL': signal_types.count('SELL'),
            'HOLD': signal_types.count('HOLD')
        }

        # Consensus levels
        consensus_levels = [s['consensus'] for s in recent_signals]
        avg_consensus = np.mean(consensus_levels) if consensus_levels else 0

        # Confidence distribution
        confidences = [s['ensemble_signal'].confidence for s in recent_signals]
        avg_confidence = np.mean(confidences) if confidences else 0

        return {
            'total_signals': len(recent_signals),
            'signal_distribution': signal_distribution,
            'average_consensus': avg_consensus,
            'average_confidence': avg_confidence,
            'ensemble_method': self.ensemble_method.value,
            'active_strategies': len(self.strategies),
            'strategy_rankings': self.get_strategy_rankings()
        }

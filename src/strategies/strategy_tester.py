"""
Comprehensive Strategy Testing System
Main system that orchestrates strategy testing, comparison, and optimization
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import json
from pathlib import Path

from ..backtesting.backtest_engine import AdvancedBacktester
from .strategy_ensemble import StrategyEnsemble, EnsembleMethod
from .strategy_analyzer import StrategyPerformanceAnalyzer
from .strategy_optimizer import StrategyOptimizer, OptimizationConfig
from .advanced_metrics import AdvancedMetricsCalculator
from .scalping_strategy import ScalpingStrategy
from .day_trading_strategy import DayTradingStrategy
from .trend_following_strategy import TrendFollowingStrategy

class ComprehensiveStrategyTester:
    """
    Comprehensive strategy testing system
    
    Features:
    - Multi-strategy backtesting
    - Ensemble method testing
    - Performance comparison
    - Strategy optimization
    - Advanced metrics calculation
    - Detailed reporting
    - CLI integration
    """
    
    def __init__(self, initial_capital: float = 100000, risk_free_rate: float = 0.02):
        """
        Initialize the comprehensive tester
        
        Args:
            initial_capital: Initial capital for backtesting
            risk_free_rate: Risk-free rate for calculations
        """
        self.initial_capital = initial_capital
        self.risk_free_rate = risk_free_rate
        self.logger = logging.getLogger("StrategyTester")
        
        # Initialize components
        self.backtester = AdvancedBacktester(initial_capital=initial_capital)
        self.analyzer = StrategyPerformanceAnalyzer(risk_free_rate=risk_free_rate)
        self.optimizer = StrategyOptimizer(self.backtester)
        self.metrics_calculator = AdvancedMetricsCalculator(risk_free_rate=risk_free_rate)
        
        # Results storage
        self.test_results = {}
        self.comparison_results = {}
        self.optimization_results = {}
        
        # Default strategies to test
        self.default_strategies = [
            ScalpingStrategy(),
            DayTradingStrategy(),
            TrendFollowingStrategy()
        ]
    
    def run_comprehensive_test(self, market_data: Dict[str, pd.DataFrame],
                             strategies: Optional[List[Any]] = None,
                             start_date: Optional[datetime] = None,
                             end_date: Optional[datetime] = None,
                             optimize_parameters: bool = True,
                             test_ensembles: bool = True) -> Dict[str, Any]:
        """
        Run comprehensive strategy testing
        
        Args:
            market_data: Market data for testing
            strategies: List of strategies to test (uses defaults if None)
            start_date: Test start date
            end_date: Test end date
            optimize_parameters: Whether to optimize strategy parameters
            test_ensembles: Whether to test ensemble methods
            
        Returns:
            Comprehensive test results
        """
        try:
            self.logger.info("Starting comprehensive strategy testing...")
            
            if strategies is None:
                strategies = self.default_strategies
            
            results = {
                'test_config': {
                    'start_date': start_date.isoformat() if start_date else None,
                    'end_date': end_date.isoformat() if end_date else None,
                    'initial_capital': self.initial_capital,
                    'strategies_tested': [s.name for s in strategies],
                    'optimize_parameters': optimize_parameters,
                    'test_ensembles': test_ensembles
                },
                'individual_results': {},
                'ensemble_results': {},
                'optimization_results': {},
                'comparison_analysis': {},
                'recommendations': {},
                'summary': {}
            }
            
            # Step 1: Test individual strategies
            self.logger.info("Testing individual strategies...")
            individual_results = self.backtester.run_multi_strategy_backtest(
                market_data, strategies, start_date, end_date
            )
            results['individual_results'] = self._process_individual_results(individual_results)
            
            # Step 2: Test ensemble methods
            if test_ensembles:
                self.logger.info("Testing ensemble methods...")
                ensemble_results = self.backtester.run_ensemble_backtest(
                    market_data, strategies, start_date=start_date, end_date=end_date
                )
                results['ensemble_results'] = self._process_ensemble_results(ensemble_results)
            
            # Step 3: Optimize parameters (if requested)
            if optimize_parameters:
                self.logger.info("Optimizing strategy parameters...")
                optimization_results = self._run_parameter_optimization(
                    strategies, market_data, start_date, end_date
                )
                results['optimization_results'] = optimization_results
            
            # Step 4: Comprehensive analysis
            self.logger.info("Performing comprehensive analysis...")
            all_results = {**individual_results}
            if test_ensembles:
                all_results.update(ensemble_results)
            
            # Strategy performance analysis
            strategy_metrics = self.analyzer.analyze_strategy_performance(all_results)
            results['comparison_analysis'] = {
                'strategy_metrics': self._serialize_metrics(strategy_metrics),
                'correlation_analysis': self._calculate_correlation_analysis(all_results),
                'optimal_combinations': self._find_optimal_combinations(strategy_metrics)
            }
            
            # Step 5: Generate recommendations
            results['recommendations'] = self.analyzer.generate_optimization_recommendations(strategy_metrics)
            
            # Step 6: Create summary
            results['summary'] = self._generate_comprehensive_summary(results)
            
            # Store results
            self.test_results = results
            
            self.logger.info("Comprehensive testing completed successfully")
            return results
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive testing: {e}")
            return {'error': str(e)}
    
    def _process_individual_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Process individual strategy results"""
        processed = {}
        
        for strategy_name, backtest_result in results.items():
            # Calculate advanced metrics
            returns_series = pd.Series(backtest_result.trade_returns) if backtest_result.trade_returns else None
            advanced_metrics = self.metrics_calculator.calculate_advanced_metrics(backtest_result, returns_series)
            
            # Calculate drawdown analysis
            equity_series = pd.Series(backtest_result.equity_curve) if backtest_result.equity_curve else None
            drawdown_analysis = None
            if equity_series is not None:
                drawdown_analysis = self.metrics_calculator.calculate_drawdown_analysis(equity_series, strategy_name)
            
            processed[strategy_name] = {
                'basic_metrics': {
                    'total_return': backtest_result.total_pnl,
                    'win_rate': backtest_result.win_rate,
                    'total_trades': backtest_result.total_trades,
                    'sharpe_ratio': backtest_result.sharpe_ratio,
                    'max_drawdown': backtest_result.max_drawdown_percent,
                    'profit_factor': backtest_result.profit_factor
                },
                'advanced_metrics': self._serialize_advanced_metrics(advanced_metrics),
                'drawdown_analysis': self._serialize_drawdown_analysis(drawdown_analysis) if drawdown_analysis else None,
                'trade_details': {
                    'winning_trades': backtest_result.winning_trades,
                    'losing_trades': backtest_result.losing_trades,
                    'avg_win': backtest_result.avg_win,
                    'avg_loss': backtest_result.avg_loss,
                    'largest_win': backtest_result.largest_win,
                    'largest_loss': backtest_result.largest_loss
                }
            }
        
        return processed
    
    def _process_ensemble_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Process ensemble strategy results"""
        processed = {}
        
        for ensemble_name, backtest_result in results.items():
            processed[ensemble_name] = {
                'method': ensemble_name.replace('Ensemble_', ''),
                'performance': {
                    'total_return': backtest_result.total_pnl,
                    'win_rate': backtest_result.win_rate,
                    'sharpe_ratio': backtest_result.sharpe_ratio,
                    'max_drawdown': backtest_result.max_drawdown_percent,
                    'total_trades': backtest_result.total_trades
                },
                'vs_individual': self._compare_to_individual(backtest_result)
            }
        
        return processed
    
    def _run_parameter_optimization(self, strategies: List[Any], 
                                  market_data: Dict[str, pd.DataFrame],
                                  start_date: Optional[datetime] = None,
                                  end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """Run parameter optimization for strategies"""
        optimization_results = {}
        
        # Define parameter ranges for each strategy type
        parameter_ranges = {
            'ScalpingStrategy': {
                'rsi_period': [10, 14, 18, 21],
                'ema_fast': [8, 12, 16],
                'ema_slow': [21, 26, 30],
                'stop_loss_atr_multiplier': [1.5, 2.0, 2.5],
                'take_profit_atr_multiplier': [2.0, 3.0, 4.0]
            },
            'DayTradingStrategy': {
                'trend_ema_fast': [10, 12, 15],
                'trend_ema_slow': [21, 26, 30],
                'rsi_period': [12, 14, 16],
                'stop_loss_atr_multiplier': [1.5, 2.0, 2.5, 3.0],
                'take_profit_atr_multiplier': [2.5, 3.0, 4.0]
            },
            'TrendFollowingStrategy': {
                'trend_ema_periods': [[15, 30, 60], [21, 50, 100], [25, 50, 120]],
                'adx_period': [12, 14, 16],
                'stop_loss_atr_multiplier': [2.5, 3.0, 3.5],
                'take_profit_atr_multiplier': [5.0, 6.0, 7.0]
            }
        }
        
        for strategy in strategies:
            strategy_class = strategy.__class__
            strategy_name = strategy_class.__name__
            
            if strategy_name in parameter_ranges:
                try:
                    self.logger.info(f"Optimizing parameters for {strategy_name}")
                    
                    optimization_result = self.optimizer.optimize_strategy_parameters(
                        strategy_class,
                        parameter_ranges[strategy_name],
                        market_data,
                        start_date,
                        end_date
                    )
                    
                    optimization_results[strategy_name] = {
                        'original_params': optimization_result.original_params,
                        'optimized_params': optimization_result.optimized_params,
                        'improvement_metrics': optimization_result.improvement_metrics,
                        'validation_results': optimization_result.validation_results
                    }
                    
                except Exception as e:
                    self.logger.error(f"Error optimizing {strategy_name}: {e}")
                    optimization_results[strategy_name] = {'error': str(e)}
        
        return optimization_results
    
    def _compare_to_individual(self, ensemble_result: Any) -> Dict[str, float]:
        """Compare ensemble performance to individual strategies"""
        # This would compare against stored individual results
        # For now, return placeholder
        return {
            'improvement_vs_best': 0.0,
            'improvement_vs_average': 0.0,
            'risk_reduction': 0.0
        }
    
    def _calculate_correlation_analysis(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate correlation analysis"""
        try:
            correlation_analysis = self.metrics_calculator.calculate_correlation_analysis(results)
            return {
                'correlation_matrix': correlation_analysis.correlation_matrix,
                'diversification_ratio': correlation_analysis.diversification_ratio,
                'effective_strategies': correlation_analysis.effective_strategies
            }
        except Exception as e:
            self.logger.error(f"Error calculating correlation analysis: {e}")
            return {}
    
    def _find_optimal_combinations(self, strategy_metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find optimal strategy combinations"""
        try:
            combinations = self.analyzer.find_optimal_combinations(strategy_metrics)
            return [
                {
                    'name': combo.combination_name,
                    'strategies': combo.strategies,
                    'weights': combo.weights,
                    'sharpe_ratio': combo.combined_metrics.sharpe_ratio,
                    'diversification_benefit': combo.diversification_benefit
                }
                for combo in combinations[:5]  # Top 5
            ]
        except Exception as e:
            self.logger.error(f"Error finding optimal combinations: {e}")
            return []
    
    def _generate_comprehensive_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive summary"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'test_duration': 'N/A',  # Would calculate actual duration
            'strategies_tested': len(results.get('individual_results', {})),
            'ensemble_methods_tested': len(results.get('ensemble_results', {})),
            'optimization_completed': bool(results.get('optimization_results', {}))
        }
        
        # Best performers
        individual_results = results.get('individual_results', {})
        if individual_results:
            best_strategy = max(individual_results.items(), 
                              key=lambda x: x[1]['basic_metrics']['sharpe_ratio'])
            summary['best_individual_strategy'] = {
                'name': best_strategy[0],
                'sharpe_ratio': best_strategy[1]['basic_metrics']['sharpe_ratio'],
                'total_return': best_strategy[1]['basic_metrics']['total_return']
            }
        
        ensemble_results = results.get('ensemble_results', {})
        if ensemble_results:
            best_ensemble = max(ensemble_results.items(),
                              key=lambda x: x[1]['performance']['sharpe_ratio'])
            summary['best_ensemble_method'] = {
                'name': best_ensemble[0],
                'sharpe_ratio': best_ensemble[1]['performance']['sharpe_ratio'],
                'total_return': best_ensemble[1]['performance']['total_return']
            }
        
        return summary
    
    def _serialize_metrics(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Serialize metrics for JSON storage"""
        serialized = {}
        for name, metric in metrics.items():
            serialized[name] = {
                'total_return': getattr(metric, 'total_return', 0),
                'sharpe_ratio': getattr(metric, 'sharpe_ratio', 0),
                'max_drawdown': getattr(metric, 'max_drawdown', 0),
                'win_rate': getattr(metric, 'win_rate', 0),
                'volatility': getattr(metric, 'volatility', 0),
                'total_trades': getattr(metric, 'total_trades', 0)
            }
        return serialized
    
    def _serialize_advanced_metrics(self, metrics: Any) -> Dict[str, Any]:
        """Serialize advanced metrics"""
        return {
            'annualized_return': getattr(metrics, 'annualized_return', 0),
            'volatility': getattr(metrics, 'volatility', 0),
            'sortino_ratio': getattr(metrics, 'sortino_ratio', 0),
            'calmar_ratio': getattr(metrics, 'calmar_ratio', 0),
            'var_95': getattr(metrics, 'var_95', 0),
            'skewness': getattr(metrics, 'skewness', 0),
            'kurtosis': getattr(metrics, 'kurtosis', 0),
            'stability': getattr(metrics, 'stability', 0)
        }
    
    def _serialize_drawdown_analysis(self, analysis: Any) -> Dict[str, Any]:
        """Serialize drawdown analysis"""
        return {
            'max_drawdown': getattr(analysis, 'max_drawdown', 0),
            'avg_drawdown': getattr(analysis, 'avg_drawdown', 0),
            'drawdown_frequency': getattr(analysis, 'drawdown_frequency', 0),
            'max_drawdown_duration_days': getattr(analysis, 'max_drawdown_duration', timedelta(0)).days
        }
    
    def save_results(self, filepath: str):
        """Save test results to file"""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
            self.logger.info(f"Results saved to {filepath}")
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")
    
    def get_cli_data(self) -> Dict[str, Any]:
        """Get data formatted for CLI display"""
        if not self.test_results:
            return {}
        
        return {
            'strategy_metrics': self._extract_cli_strategy_metrics(),
            'ensemble_results': self._extract_cli_ensemble_results(),
            'rankings': self._extract_cli_rankings(),
            'recommendations': self.test_results.get('recommendations', {})
        }
    
    def _extract_cli_strategy_metrics(self) -> Dict[str, Any]:
        """Extract strategy metrics for CLI"""
        cli_metrics = {}
        individual_results = self.test_results.get('individual_results', {})
        
        for name, results in individual_results.items():
            basic_metrics = results.get('basic_metrics', {})
            cli_metrics[name] = {
                'total_return': basic_metrics.get('total_return', 0),
                'win_rate': basic_metrics.get('win_rate', 0),
                'sharpe_ratio': basic_metrics.get('sharpe_ratio', 0),
                'max_drawdown': basic_metrics.get('max_drawdown', 0),
                'total_trades': basic_metrics.get('total_trades', 0),
                'active': True
            }
        
        return cli_metrics
    
    def _extract_cli_ensemble_results(self) -> Dict[str, Any]:
        """Extract ensemble results for CLI"""
        cli_ensemble = {}
        ensemble_results = self.test_results.get('ensemble_results', {})
        
        for name, results in ensemble_results.items():
            performance = results.get('performance', {})
            cli_ensemble[name] = {
                'total_return': performance.get('total_return', 0),
                'sharpe_ratio': performance.get('sharpe_ratio', 0),
                'win_rate': performance.get('win_rate', 0),
                'vs_best_individual': results.get('vs_individual', {}).get('improvement_vs_best', 0),
                'avg_confidence': 0.75,  # Placeholder
                'consensus_level': 0.65   # Placeholder
            }
        
        return cli_ensemble
    
    def _extract_cli_rankings(self) -> List[Dict[str, Any]]:
        """Extract strategy rankings for CLI"""
        rankings = []
        individual_results = self.test_results.get('individual_results', {})
        
        for name, results in individual_results.items():
            basic_metrics = results.get('basic_metrics', {})
            rankings.append({
                'strategy': name,
                'score': basic_metrics.get('sharpe_ratio', 0),
                'trend': 'stable'  # Would be calculated from historical data
            })
        
        # Sort by score
        rankings.sort(key=lambda x: x['score'], reverse=True)
        
        return rankings

"""
Base Strategy Class
Foundation for all trading strategies with common functionality
"""

import logging
import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

@dataclass
class Signal:
    """Trading signal data structure"""
    timestamp: datetime
    instrument: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0.0 to 1.0
    entry_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    strategy_name: str = ""
    timeframe: str = ""
    reasoning: str = ""
    metadata: Dict = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

@dataclass
class StrategyPerformance:
    """Strategy performance metrics"""
    total_signals: int = 0
    winning_signals: int = 0
    losing_signals: int = 0
    win_rate: float = 0.0
    avg_profit: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    total_return: float = 0.0

class BaseStrategy(ABC):
    """Base class for all trading strategies"""
    
    def __init__(self, name: str, timeframes: List[str] = None, parameters: Dict = None):
        """
        Initialize base strategy
        
        Args:
            name: Strategy name
            timeframes: List of timeframes this strategy uses
            parameters: Strategy-specific parameters
        """
        self.name = name
        self.timeframes = timeframes or ['H1']
        self.parameters = parameters or {}
        self.logger = logging.getLogger(f"Strategy.{name}")
        
        # Performance tracking
        self.performance = StrategyPerformance()
        self.signals_history = []
        self.is_active = True
        
        # Strategy state
        self.last_signal = None
        self.last_analysis_time = None
        
        self.logger.info(f"Strategy {name} initialized with timeframes: {self.timeframes}")
    
    @abstractmethod
    def analyze(self, market_data: Dict) -> Signal:
        """
        Analyze market data and generate trading signal
        
        Args:
            market_data: Market data dictionary with OHLCV and indicators
            
        Returns:
            Signal object with trading recommendation
        """
        pass
    
    @abstractmethod
    def get_required_indicators(self) -> List[str]:
        """
        Get list of required technical indicators for this strategy
        
        Returns:
            List of indicator names
        """
        pass
    
    def validate_market_data(self, market_data: Dict) -> bool:
        """
        Validate that market data contains required information
        
        Args:
            market_data: Market data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        try:
            required_fields = ['instrument', 'current_price', 'timestamp']
            for field in required_fields:
                if field not in market_data:
                    self.logger.warning(f"Missing required field: {field}")
                    return False
            
            # Check for required timeframes
            for timeframe in self.timeframes:
                if timeframe not in market_data:
                    self.logger.warning(f"Missing timeframe data: {timeframe}")
                    return False
            
            # Check for required indicators
            required_indicators = self.get_required_indicators()
            for timeframe in self.timeframes:
                tf_data = market_data[timeframe]
                for indicator in required_indicators:
                    if indicator not in tf_data.columns:
                        self.logger.warning(f"Missing indicator {indicator} in {timeframe}")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating market data: {e}")
            return False
    
    def calculate_position_size(self, signal: Signal, account_balance: float, 
                              risk_per_trade: float = 0.02) -> int:
        """
        Calculate position size based on risk management
        
        Args:
            signal: Trading signal
            account_balance: Current account balance
            risk_per_trade: Risk percentage per trade (default 2%)
            
        Returns:
            Position size in units
        """
        try:
            if not signal.stop_loss:
                # Use default 1% of balance if no stop loss
                return int(account_balance * 0.01)
            
            # Calculate risk amount
            risk_amount = account_balance * risk_per_trade
            
            # Calculate stop loss distance
            stop_distance = abs(signal.entry_price - signal.stop_loss)
            
            # Calculate position size
            if stop_distance > 0:
                position_size = int(risk_amount / stop_distance)
                return max(1, min(position_size, int(account_balance * 0.1)))  # Max 10% of balance
            else:
                return int(account_balance * 0.01)
                
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return int(account_balance * 0.01)
    
    def update_performance(self, signal: Signal, actual_result: Dict):
        """
        Update strategy performance metrics
        
        Args:
            signal: Original signal
            actual_result: Actual trading result
        """
        try:
            self.performance.total_signals += 1
            
            profit_loss = actual_result.get('profit_loss', 0)
            
            if profit_loss > 0:
                self.performance.winning_signals += 1
                self.performance.avg_profit = (
                    (self.performance.avg_profit * (self.performance.winning_signals - 1) + profit_loss) /
                    self.performance.winning_signals
                )
            elif profit_loss < 0:
                self.performance.losing_signals += 1
                self.performance.avg_loss = (
                    (self.performance.avg_loss * (self.performance.losing_signals - 1) + abs(profit_loss)) /
                    self.performance.losing_signals
                )
            
            # Update win rate
            self.performance.win_rate = self.performance.winning_signals / self.performance.total_signals
            
            # Update profit factor
            total_profit = self.performance.avg_profit * self.performance.winning_signals
            total_loss = self.performance.avg_loss * self.performance.losing_signals
            
            if total_loss > 0:
                self.performance.profit_factor = total_profit / total_loss
            
            # Update total return
            self.performance.total_return += profit_loss
            
        except Exception as e:
            self.logger.error(f"Error updating performance: {e}")
    
    def get_signal_strength(self, signal: Signal) -> str:
        """
        Get signal strength description based on confidence
        
        Args:
            signal: Trading signal
            
        Returns:
            Signal strength description
        """
        if signal.confidence >= 0.8:
            return "STRONG"
        elif signal.confidence >= 0.6:
            return "MODERATE"
        elif signal.confidence >= 0.4:
            return "WEAK"
        else:
            return "VERY_WEAK"
    
    def should_trade(self, signal: Signal, current_time: datetime = None) -> bool:
        """
        Determine if signal should be traded based on strategy rules
        
        Args:
            signal: Trading signal
            current_time: Current timestamp
            
        Returns:
            True if signal should be traded
        """
        try:
            # Check if strategy is active
            if not self.is_active:
                return False
            
            # Check signal confidence
            if signal.confidence < 0.6:
                return False
            
            # Check if signal is not HOLD
            if signal.signal_type == 'HOLD':
                return False
            
            # Check if we have a valid entry price
            if not signal.entry_price or signal.entry_price <= 0:
                return False
            
            # Strategy-specific validation can be overridden
            return self._custom_trade_validation(signal, current_time)
            
        except Exception as e:
            self.logger.error(f"Error in should_trade: {e}")
            return False
    
    def _custom_trade_validation(self, signal: Signal, current_time: datetime = None) -> bool:
        """
        Custom trade validation - can be overridden by specific strategies
        
        Args:
            signal: Trading signal
            current_time: Current timestamp
            
        Returns:
            True if signal passes custom validation
        """
        return True
    
    def get_strategy_info(self) -> Dict:
        """
        Get strategy information and current status
        
        Returns:
            Dictionary with strategy information
        """
        return {
            'name': self.name,
            'timeframes': self.timeframes,
            'parameters': self.parameters,
            'is_active': self.is_active,
            'performance': {
                'total_signals': self.performance.total_signals,
                'win_rate': self.performance.win_rate,
                'profit_factor': self.performance.profit_factor,
                'total_return': self.performance.total_return
            },
            'last_signal': {
                'timestamp': self.last_signal.timestamp if self.last_signal else None,
                'signal_type': self.last_signal.signal_type if self.last_signal else None,
                'confidence': self.last_signal.confidence if self.last_signal else None
            } if self.last_signal else None
        }
    
    def reset_performance(self):
        """Reset strategy performance metrics"""
        self.performance = StrategyPerformance()
        self.signals_history = []
        self.logger.info(f"Performance metrics reset for strategy {self.name}")
    
    def set_active(self, active: bool):
        """Enable or disable strategy"""
        self.is_active = active
        status = "activated" if active else "deactivated"
        self.logger.info(f"Strategy {self.name} {status}")
    
    def update_parameters(self, new_parameters: Dict):
        """Update strategy parameters"""
        self.parameters.update(new_parameters)
        self.logger.info(f"Parameters updated for strategy {self.name}: {new_parameters}")
    
    def __str__(self):
        return f"Strategy({self.name}, timeframes={self.timeframes}, active={self.is_active})"
    
    def __repr__(self):
        return self.__str__()

"""
Scalping Strategy
High-frequency trading strategy for quick profits on small price movements
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List
from .base_strategy import BaseStrategy, Signal

class ScalpingStrategy(BaseStrategy):
    """
    Advanced scalping strategy using multiple indicators and AI confirmation
    
    Features:
    - 1-minute and 5-minute timeframes
    - Quick entry/exit signals
    - Tight stop losses and take profits
    - High win rate focus
    - Momentum and volatility based
    """
    
    def __init__(self, parameters: Dict = None):
        default_params = {
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'ema_fast': 8,
            'ema_slow': 21,
            'bb_period': 20,
            'bb_std': 2,
            'atr_period': 14,
            'volume_threshold': 1.5,  # Volume multiplier
            'min_spread_ratio': 0.5,  # Minimum spread to ATR ratio
            'max_spread_ratio': 2.0,  # Maximum spread to ATR ratio
            'stop_loss_atr_multiplier': 1.5,
            'take_profit_atr_multiplier': 2.0,
            'min_confidence': 0.7
        }
        
        if parameters:
            default_params.update(parameters)
        
        super().__init__(
            name="Scalping",
            timeframes=['M1', 'M5'],
            parameters=default_params
        )
    
    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators"""
        return [
            'rsi', 'ema_fast', 'ema_slow', 'bb_upper', 'bb_lower', 'bb_middle',
            'atr', 'volume', 'vwap', 'macd', 'macd_signal'
        ]
    
    def analyze(self, market_data: Dict) -> Signal:
        """
        Analyze market for scalping opportunities
        
        Args:
            market_data: Market data with M1 and M5 timeframes
            
        Returns:
            Scalping signal
        """
        try:
            if not self.validate_market_data(market_data):
                return self._create_hold_signal(market_data)
            
            # Get current market info
            instrument = market_data['instrument']
            current_price = market_data['current_price']['mid']
            spread = market_data['current_price']['spread']
            timestamp = datetime.now()
            
            # Get timeframe data
            m1_data = market_data['M1']
            m5_data = market_data['M5']
            
            # Check if we have enough data
            if len(m1_data) < 50 or len(m5_data) < 50:
                return self._create_hold_signal(market_data)
            
            # Get latest values
            m1_latest = m1_data.iloc[-1]
            m5_latest = m5_data.iloc[-1]
            
            # Calculate ATR for stop loss/take profit
            atr = m1_latest['atr']
            
            # Check spread conditions
            if not self._is_spread_acceptable(spread, atr):
                return self._create_hold_signal(market_data, "Spread too wide for scalping")
            
            # Analyze market conditions
            market_condition = self._analyze_market_condition(m1_data, m5_data)
            
            # Generate signal based on multiple factors
            signal_analysis = self._analyze_scalping_signals(m1_data, m5_data, current_price)
            
            # Combine signals and calculate confidence
            final_signal = self._combine_signals(signal_analysis, market_condition)
            
            # Create signal object
            signal = Signal(
                timestamp=timestamp,
                instrument=instrument,
                signal_type=final_signal['direction'],
                confidence=final_signal['confidence'],
                entry_price=current_price,
                stop_loss=self._calculate_stop_loss(current_price, final_signal['direction'], atr),
                take_profit=self._calculate_take_profit(current_price, final_signal['direction'], atr),
                strategy_name=self.name,
                timeframe="M1",
                reasoning=final_signal['reasoning'],
                metadata={
                    'atr': atr,
                    'spread': spread,
                    'market_condition': market_condition,
                    'signal_strength': self.get_signal_strength(Signal(
                        timestamp=timestamp, instrument=instrument, 
                        signal_type=final_signal['direction'], 
                        confidence=final_signal['confidence'], 
                        entry_price=current_price
                    ))
                }
            )
            
            self.last_signal = signal
            self.last_analysis_time = timestamp
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error in scalping analysis: {e}")
            return self._create_hold_signal(market_data, f"Analysis error: {str(e)}")
    
    def _is_spread_acceptable(self, spread: float, atr: float) -> bool:
        """Check if spread is acceptable for scalping"""
        if atr <= 0:
            return False
        
        spread_ratio = spread / atr
        min_ratio = self.parameters['min_spread_ratio']
        max_ratio = self.parameters['max_spread_ratio']
        
        return min_ratio <= spread_ratio <= max_ratio
    
    def _analyze_market_condition(self, m1_data: pd.DataFrame, m5_data: pd.DataFrame) -> Dict:
        """Analyze overall market condition"""
        try:
            m1_latest = m1_data.iloc[-1]
            m5_latest = m5_data.iloc[-1]
            
            # Volatility analysis
            atr_current = m1_latest['atr']
            atr_avg = m1_data['atr'].tail(20).mean()
            volatility_ratio = atr_current / atr_avg if atr_avg > 0 else 1
            
            # Trend analysis
            ema_fast_m5 = m5_latest['ema_fast']
            ema_slow_m5 = m5_latest['ema_slow']
            trend_strength = abs(ema_fast_m5 - ema_slow_m5) / ema_slow_m5 if ema_slow_m5 > 0 else 0
            
            # Volume analysis
            volume_current = m1_latest['volume']
            volume_avg = m1_data['volume'].tail(20).mean()
            volume_ratio = volume_current / volume_avg if volume_avg > 0 else 1
            
            # Market condition classification
            if volatility_ratio > 1.5 and volume_ratio > self.parameters['volume_threshold']:
                condition = "HIGH_VOLATILITY"
            elif trend_strength > 0.002:  # Strong trend
                condition = "TRENDING"
            elif volatility_ratio < 0.7:
                condition = "LOW_VOLATILITY"
            else:
                condition = "NORMAL"
            
            return {
                'condition': condition,
                'volatility_ratio': volatility_ratio,
                'trend_strength': trend_strength,
                'volume_ratio': volume_ratio
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing market condition: {e}")
            return {'condition': 'UNKNOWN', 'volatility_ratio': 1, 'trend_strength': 0, 'volume_ratio': 1}
    
    def _analyze_scalping_signals(self, m1_data: pd.DataFrame, m5_data: pd.DataFrame, 
                                current_price: float) -> Dict:
        """Analyze multiple scalping signals"""
        try:
            m1_latest = m1_data.iloc[-1]
            m5_latest = m5_data.iloc[-1]
            
            signals = {}
            
            # 1. RSI Divergence Signal
            signals['rsi'] = self._analyze_rsi_signal(m1_data, current_price)
            
            # 2. EMA Crossover Signal
            signals['ema'] = self._analyze_ema_signal(m1_data, m5_data)
            
            # 3. Bollinger Bands Signal
            signals['bb'] = self._analyze_bollinger_signal(m1_latest, current_price)
            
            # 4. MACD Signal
            signals['macd'] = self._analyze_macd_signal(m1_data)
            
            # 5. Volume Confirmation
            signals['volume'] = self._analyze_volume_signal(m1_data)
            
            # 6. VWAP Signal
            signals['vwap'] = self._analyze_vwap_signal(m1_latest, current_price)
            
            return signals
            
        except Exception as e:
            self.logger.error(f"Error analyzing scalping signals: {e}")
            return {}
    
    def _analyze_rsi_signal(self, m1_data: pd.DataFrame, current_price: float) -> Dict:
        """Analyze RSI for scalping signals"""
        try:
            rsi_current = m1_data.iloc[-1]['rsi']
            rsi_prev = m1_data.iloc[-2]['rsi']
            
            oversold = self.parameters['rsi_oversold']
            overbought = self.parameters['rsi_overbought']
            
            if rsi_current < oversold and rsi_prev >= rsi_current:
                return {'direction': 'BUY', 'strength': 0.8, 'reason': 'RSI oversold bounce'}
            elif rsi_current > overbought and rsi_prev <= rsi_current:
                return {'direction': 'SELL', 'strength': 0.8, 'reason': 'RSI overbought reversal'}
            elif 40 <= rsi_current <= 60:
                return {'direction': 'NEUTRAL', 'strength': 0.3, 'reason': 'RSI neutral zone'}
            else:
                return {'direction': 'HOLD', 'strength': 0.1, 'reason': 'RSI no clear signal'}
                
        except Exception as e:
            return {'direction': 'HOLD', 'strength': 0, 'reason': f'RSI error: {e}'}
    
    def _analyze_ema_signal(self, m1_data: pd.DataFrame, m5_data: pd.DataFrame) -> Dict:
        """Analyze EMA crossover signals"""
        try:
            # M1 EMA analysis
            m1_latest = m1_data.iloc[-1]
            m1_prev = m1_data.iloc[-2]
            
            ema_fast_current = m1_latest['ema_fast']
            ema_slow_current = m1_latest['ema_slow']
            ema_fast_prev = m1_prev['ema_fast']
            ema_slow_prev = m1_prev['ema_slow']
            
            # Check for crossover
            if ema_fast_prev <= ema_slow_prev and ema_fast_current > ema_slow_current:
                return {'direction': 'BUY', 'strength': 0.7, 'reason': 'EMA bullish crossover'}
            elif ema_fast_prev >= ema_slow_prev and ema_fast_current < ema_slow_current:
                return {'direction': 'SELL', 'strength': 0.7, 'reason': 'EMA bearish crossover'}
            
            # Check trend alignment with M5
            m5_latest = m5_data.iloc[-1]
            m5_ema_fast = m5_latest['ema_fast']
            m5_ema_slow = m5_latest['ema_slow']
            
            if ema_fast_current > ema_slow_current and m5_ema_fast > m5_ema_slow:
                return {'direction': 'BUY', 'strength': 0.5, 'reason': 'EMA bullish alignment'}
            elif ema_fast_current < ema_slow_current and m5_ema_fast < m5_ema_slow:
                return {'direction': 'SELL', 'strength': 0.5, 'reason': 'EMA bearish alignment'}
            
            return {'direction': 'HOLD', 'strength': 0.2, 'reason': 'EMA no clear signal'}
            
        except Exception as e:
            return {'direction': 'HOLD', 'strength': 0, 'reason': f'EMA error: {e}'}
    
    def _analyze_bollinger_signal(self, m1_latest: pd.Series, current_price: float) -> Dict:
        """Analyze Bollinger Bands signals"""
        try:
            bb_upper = m1_latest['bb_upper']
            bb_lower = m1_latest['bb_lower']
            bb_middle = m1_latest['bb_middle']
            
            # Calculate position within bands
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
            
            if bb_position <= 0.1:  # Near lower band
                return {'direction': 'BUY', 'strength': 0.6, 'reason': 'Price near BB lower band'}
            elif bb_position >= 0.9:  # Near upper band
                return {'direction': 'SELL', 'strength': 0.6, 'reason': 'Price near BB upper band'}
            elif 0.4 <= bb_position <= 0.6:  # Near middle
                return {'direction': 'NEUTRAL', 'strength': 0.3, 'reason': 'Price near BB middle'}
            else:
                return {'direction': 'HOLD', 'strength': 0.2, 'reason': 'BB no clear signal'}
                
        except Exception as e:
            return {'direction': 'HOLD', 'strength': 0, 'reason': f'BB error: {e}'}
    
    def _analyze_macd_signal(self, m1_data: pd.DataFrame) -> Dict:
        """Analyze MACD signals"""
        try:
            macd_current = m1_data.iloc[-1]['macd']
            macd_signal_current = m1_data.iloc[-1]['macd_signal']
            macd_prev = m1_data.iloc[-2]['macd']
            macd_signal_prev = m1_data.iloc[-2]['macd_signal']
            
            # MACD crossover
            if macd_prev <= macd_signal_prev and macd_current > macd_signal_current:
                return {'direction': 'BUY', 'strength': 0.6, 'reason': 'MACD bullish crossover'}
            elif macd_prev >= macd_signal_prev and macd_current < macd_signal_current:
                return {'direction': 'SELL', 'strength': 0.6, 'reason': 'MACD bearish crossover'}
            
            # MACD momentum
            if macd_current > macd_signal_current and macd_current > 0:
                return {'direction': 'BUY', 'strength': 0.4, 'reason': 'MACD bullish momentum'}
            elif macd_current < macd_signal_current and macd_current < 0:
                return {'direction': 'SELL', 'strength': 0.4, 'reason': 'MACD bearish momentum'}
            
            return {'direction': 'HOLD', 'strength': 0.2, 'reason': 'MACD no clear signal'}
            
        except Exception as e:
            return {'direction': 'HOLD', 'strength': 0, 'reason': f'MACD error: {e}'}
    
    def _analyze_volume_signal(self, m1_data: pd.DataFrame) -> Dict:
        """Analyze volume confirmation"""
        try:
            volume_current = m1_data.iloc[-1]['volume']
            volume_avg = m1_data['volume'].tail(10).mean()
            
            volume_ratio = volume_current / volume_avg if volume_avg > 0 else 1
            
            if volume_ratio >= self.parameters['volume_threshold']:
                return {'direction': 'CONFIRM', 'strength': 0.5, 'reason': 'High volume confirmation'}
            else:
                return {'direction': 'WEAK', 'strength': 0.2, 'reason': 'Low volume'}
                
        except Exception as e:
            return {'direction': 'WEAK', 'strength': 0, 'reason': f'Volume error: {e}'}
    
    def _analyze_vwap_signal(self, m1_latest: pd.Series, current_price: float) -> Dict:
        """Analyze VWAP signals"""
        try:
            vwap = m1_latest['vwap']
            
            price_vs_vwap = (current_price - vwap) / vwap if vwap > 0 else 0
            
            if price_vs_vwap < -0.001:  # Price below VWAP
                return {'direction': 'BUY', 'strength': 0.4, 'reason': 'Price below VWAP'}
            elif price_vs_vwap > 0.001:  # Price above VWAP
                return {'direction': 'SELL', 'strength': 0.4, 'reason': 'Price above VWAP'}
            else:
                return {'direction': 'NEUTRAL', 'strength': 0.2, 'reason': 'Price near VWAP'}
                
        except Exception as e:
            return {'direction': 'HOLD', 'strength': 0, 'reason': f'VWAP error: {e}'}
    
    def _combine_signals(self, signals: Dict, market_condition: Dict) -> Dict:
        """Combine all signals into final decision"""
        try:
            buy_strength = 0
            sell_strength = 0
            total_weight = 0
            reasons = []
            
            # Weight different signals
            signal_weights = {
                'rsi': 0.25,
                'ema': 0.25,
                'bb': 0.20,
                'macd': 0.15,
                'volume': 0.10,
                'vwap': 0.05
            }
            
            for signal_name, weight in signal_weights.items():
                if signal_name in signals:
                    signal = signals[signal_name]
                    strength = signal.get('strength', 0)
                    direction = signal.get('direction', 'HOLD')
                    
                    if direction == 'BUY':
                        buy_strength += strength * weight
                        reasons.append(signal.get('reason', ''))
                    elif direction == 'SELL':
                        sell_strength += strength * weight
                        reasons.append(signal.get('reason', ''))
                    elif direction == 'CONFIRM' and signal_name == 'volume':
                        # Volume confirmation boosts existing signals
                        buy_strength *= 1.2
                        sell_strength *= 1.2
                    
                    total_weight += weight
            
            # Adjust for market conditions
            condition = market_condition.get('condition', 'NORMAL')
            if condition == 'HIGH_VOLATILITY':
                # Reduce confidence in high volatility
                buy_strength *= 0.8
                sell_strength *= 0.8
            elif condition == 'LOW_VOLATILITY':
                # Avoid trading in low volatility
                buy_strength *= 0.5
                sell_strength *= 0.5
            
            # Determine final signal
            if buy_strength > sell_strength and buy_strength >= self.parameters['min_confidence']:
                direction = 'BUY'
                confidence = min(buy_strength, 1.0)
            elif sell_strength > buy_strength and sell_strength >= self.parameters['min_confidence']:
                direction = 'SELL'
                confidence = min(sell_strength, 1.0)
            else:
                direction = 'HOLD'
                confidence = max(buy_strength, sell_strength)
            
            reasoning = f"Scalping signal: {direction} (confidence: {confidence:.2f}). " + \
                       f"Market: {condition}. Signals: {', '.join(reasons[:3])}"
            
            return {
                'direction': direction,
                'confidence': confidence,
                'reasoning': reasoning
            }
            
        except Exception as e:
            self.logger.error(f"Error combining signals: {e}")
            return {
                'direction': 'HOLD',
                'confidence': 0.0,
                'reasoning': f"Signal combination error: {str(e)}"
            }
    
    def _calculate_stop_loss(self, entry_price: float, direction: str, atr: float) -> float:
        """Calculate stop loss based on ATR"""
        multiplier = self.parameters['stop_loss_atr_multiplier']
        
        if direction == 'BUY':
            return entry_price - (atr * multiplier)
        else:  # SELL
            return entry_price + (atr * multiplier)
    
    def _calculate_take_profit(self, entry_price: float, direction: str, atr: float) -> float:
        """Calculate take profit based on ATR"""
        multiplier = self.parameters['take_profit_atr_multiplier']
        
        if direction == 'BUY':
            return entry_price + (atr * multiplier)
        else:  # SELL
            return entry_price - (atr * multiplier)
    
    def _create_hold_signal(self, market_data: Dict, reason: str = "No trading opportunity") -> Signal:
        """Create a HOLD signal"""
        return Signal(
            timestamp=datetime.now(),
            instrument=market_data.get('instrument', 'UNKNOWN'),
            signal_type='HOLD',
            confidence=0.0,
            entry_price=market_data.get('current_price', {}).get('mid', 0),
            strategy_name=self.name,
            timeframe="M1",
            reasoning=reason
        )

    def _custom_trade_validation(self, signal: Signal, current_time: datetime = None) -> bool:
        """
        Custom trade validation for scalping

        Args:
            signal: Trading signal
            current_time: Current timestamp

        Returns:
            True if signal passes scalping-specific validation
        """
        try:
            # Scalping specific validations

            # 1. Check if stop loss and take profit are reasonable
            if signal.stop_loss and signal.take_profit:
                stop_distance = abs(signal.entry_price - signal.stop_loss)
                profit_distance = abs(signal.entry_price - signal.take_profit)

                # Risk/reward ratio should be at least 1:1.3 for scalping
                if profit_distance / stop_distance < 1.3:
                    return False

            # 2. Check time-based restrictions (avoid news times, market open/close)
            if current_time:
                hour = current_time.hour
                minute = current_time.minute

                # Avoid trading during major news times
                news_times = [(8, 30), (10, 0), (14, 30), (16, 0)]
                for news_hour, news_minute in news_times:
                    if hour == news_hour and abs(minute - news_minute) <= 15:
                        return False

                # Avoid trading during low liquidity hours
                if hour < 6 or hour > 21:  # Outside major trading sessions
                    return False

            # 3. Check signal freshness (scalping signals should be very recent)
            if signal.timestamp and current_time:
                signal_age = (current_time - signal.timestamp).total_seconds()
                if signal_age > 60:  # Signal older than 1 minute
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Error in scalping trade validation: {e}")
            return False

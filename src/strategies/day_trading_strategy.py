"""
Day Trading Strategy
Medium-term intraday trading strategy focusing on trend following and breakouts
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List
from .base_strategy import BaseStrategy, Signal

class DayTradingStrategy(BaseStrategy):
    """
    Advanced day trading strategy using trend analysis and breakout detection
    
    Features:
    - 15-minute and 1-hour timeframes
    - Trend following with momentum confirmation
    - Breakout detection and validation
    - Support/resistance levels
    - Multi-timeframe analysis
    """
    
    def __init__(self, parameters: Dict = None):
        default_params = {
            'trend_ema_fast': 12,
            'trend_ema_slow': 26,
            'momentum_period': 14,
            'rsi_period': 14,
            'rsi_oversold': 35,
            'rsi_overbought': 65,
            'atr_period': 14,
            'bb_period': 20,
            'bb_std': 2,
            'volume_ma_period': 20,
            'volume_threshold': 1.3,
            'breakout_lookback': 20,
            'support_resistance_periods': [10, 20, 50],
            'min_trend_strength': 0.001,
            'stop_loss_atr_multiplier': 2.0,
            'take_profit_atr_multiplier': 3.0,
            'min_confidence': 0.65
        }
        
        if parameters:
            default_params.update(parameters)
        
        super().__init__(
            name="DayTrading",
            timeframes=['M15', 'H1', 'H4'],
            parameters=default_params
        )
    
    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators"""
        return [
            'ema_fast', 'ema_slow', 'rsi', 'atr', 'bb_upper', 'bb_lower', 'bb_middle',
            'volume', 'vwap', 'macd', 'macd_signal', 'stoch_k', 'stoch_d',
            'adx', 'plus_di', 'minus_di'
        ]
    
    def analyze(self, market_data: Dict) -> Signal:
        """
        Analyze market for day trading opportunities
        
        Args:
            market_data: Market data with M15, H1, and H4 timeframes
            
        Returns:
            Day trading signal
        """
        try:
            if not self.validate_market_data(market_data):
                return self._create_hold_signal(market_data)
            
            # Get current market info
            instrument = market_data['instrument']
            current_price = market_data['current_price']['mid']
            timestamp = datetime.now()
            
            # Get timeframe data
            m15_data = market_data['M15']
            h1_data = market_data['H1']
            h4_data = market_data['H4']
            
            # Check if we have enough data
            if len(m15_data) < 100 or len(h1_data) < 100 or len(h4_data) < 50:
                return self._create_hold_signal(market_data)
            
            # Get latest values
            m15_latest = m15_data.iloc[-1]
            h1_latest = h1_data.iloc[-1]
            h4_latest = h4_data.iloc[-1]
            
            # Calculate ATR for stop loss/take profit
            atr = m15_latest['atr']
            
            # Analyze market structure
            market_structure = self._analyze_market_structure(m15_data, h1_data, h4_data)
            
            # Analyze trend across timeframes
            trend_analysis = self._analyze_multi_timeframe_trend(m15_data, h1_data, h4_data)
            
            # Detect breakouts and key levels
            breakout_analysis = self._analyze_breakouts(m15_data, current_price)
            
            # Analyze momentum and oscillators
            momentum_analysis = self._analyze_momentum(m15_data, h1_data)
            
            # Volume analysis
            volume_analysis = self._analyze_volume_profile(m15_data)
            
            # Combine all analyses
            final_signal = self._combine_day_trading_signals(
                trend_analysis, breakout_analysis, momentum_analysis, 
                volume_analysis, market_structure, current_price
            )
            
            # Create signal object
            signal = Signal(
                timestamp=timestamp,
                instrument=instrument,
                signal_type=final_signal['direction'],
                confidence=final_signal['confidence'],
                entry_price=current_price,
                stop_loss=self._calculate_stop_loss(current_price, final_signal['direction'], atr),
                take_profit=self._calculate_take_profit(current_price, final_signal['direction'], atr),
                strategy_name=self.name,
                timeframe="M15",
                reasoning=final_signal['reasoning'],
                metadata={
                    'atr': atr,
                    'trend_strength': trend_analysis.get('strength', 0),
                    'market_structure': market_structure,
                    'breakout_type': breakout_analysis.get('type', 'none'),
                    'volume_confirmation': volume_analysis.get('confirmation', False)
                }
            )
            
            self.last_signal = signal
            self.last_analysis_time = timestamp
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error in day trading analysis: {e}")
            return self._create_hold_signal(market_data, f"Analysis error: {str(e)}")
    
    def _analyze_market_structure(self, m15_data: pd.DataFrame, h1_data: pd.DataFrame, 
                                h4_data: pd.DataFrame) -> Dict:
        """Analyze overall market structure"""
        try:
            # Higher highs and higher lows analysis
            h4_highs = h4_data['high'].tail(10)
            h4_lows = h4_data['low'].tail(10)
            
            # Trend structure
            recent_highs = h4_highs.tail(3).values
            recent_lows = h4_lows.tail(3).values
            
            higher_highs = all(recent_highs[i] >= recent_highs[i-1] for i in range(1, len(recent_highs)))
            higher_lows = all(recent_lows[i] >= recent_lows[i-1] for i in range(1, len(recent_lows)))
            lower_highs = all(recent_highs[i] <= recent_highs[i-1] for i in range(1, len(recent_highs)))
            lower_lows = all(recent_lows[i] <= recent_lows[i-1] for i in range(1, len(recent_lows)))
            
            if higher_highs and higher_lows:
                structure = "UPTREND"
            elif lower_highs and lower_lows:
                structure = "DOWNTREND"
            else:
                structure = "SIDEWAYS"
            
            # Volatility analysis
            h1_atr = h1_data['atr'].tail(20).mean()
            current_atr = h1_data['atr'].iloc[-1]
            volatility_ratio = current_atr / h1_atr if h1_atr > 0 else 1
            
            return {
                'trend_structure': structure,
                'volatility_ratio': volatility_ratio,
                'higher_highs': higher_highs,
                'higher_lows': higher_lows,
                'lower_highs': lower_highs,
                'lower_lows': lower_lows
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing market structure: {e}")
            return {'trend_structure': 'UNKNOWN', 'volatility_ratio': 1}
    
    def _analyze_multi_timeframe_trend(self, m15_data: pd.DataFrame, h1_data: pd.DataFrame, 
                                     h4_data: pd.DataFrame) -> Dict:
        """Analyze trend across multiple timeframes"""
        try:
            trends = {}
            
            # Analyze each timeframe
            for name, data in [('M15', m15_data), ('H1', h1_data), ('H4', h4_data)]:
                latest = data.iloc[-1]
                
                # EMA trend
                ema_fast = latest['ema_fast']
                ema_slow = latest['ema_slow']
                ema_trend = 'BULLISH' if ema_fast > ema_slow else 'BEARISH'
                
                # ADX trend strength
                adx = latest.get('adx', 25)
                trend_strength = 'STRONG' if adx > 25 else 'WEAK'
                
                # Price vs EMA
                current_price = latest['close']
                price_vs_ema = (current_price - ema_fast) / ema_fast if ema_fast > 0 else 0
                
                trends[name] = {
                    'direction': ema_trend,
                    'strength': trend_strength,
                    'adx': adx,
                    'price_vs_ema': price_vs_ema
                }
            
            # Calculate overall trend alignment
            bullish_count = sum(1 for tf in trends.values() if tf['direction'] == 'BULLISH')
            bearish_count = sum(1 for tf in trends.values() if tf['direction'] == 'BEARISH')
            
            if bullish_count >= 2:
                overall_trend = 'BULLISH'
                alignment_strength = bullish_count / 3
            elif bearish_count >= 2:
                overall_trend = 'BEARISH'
                alignment_strength = bearish_count / 3
            else:
                overall_trend = 'MIXED'
                alignment_strength = 0.5
            
            # Calculate trend strength
            avg_adx = np.mean([tf['adx'] for tf in trends.values()])
            strength_score = min(avg_adx / 30, 1.0)  # Normalize to 0-1
            
            return {
                'overall_direction': overall_trend,
                'alignment_strength': alignment_strength,
                'strength_score': strength_score,
                'timeframe_trends': trends,
                'avg_adx': avg_adx
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing multi-timeframe trend: {e}")
            return {'overall_direction': 'MIXED', 'alignment_strength': 0.5, 'strength_score': 0.5}
    
    def _analyze_breakouts(self, m15_data: pd.DataFrame, current_price: float) -> Dict:
        """Analyze breakout patterns"""
        try:
            lookback = self.parameters['breakout_lookback']
            
            # Get recent high and low
            recent_data = m15_data.tail(lookback)
            resistance_level = recent_data['high'].max()
            support_level = recent_data['low'].min()
            
            # Calculate breakout thresholds
            price_range = resistance_level - support_level
            breakout_threshold = price_range * 0.1  # 10% of range
            
            # Check for breakouts
            breakout_type = 'none'
            breakout_strength = 0
            
            if current_price > resistance_level + breakout_threshold:
                breakout_type = 'resistance_breakout'
                breakout_strength = min((current_price - resistance_level) / price_range, 1.0)
            elif current_price < support_level - breakout_threshold:
                breakout_type = 'support_breakdown'
                breakout_strength = min((support_level - current_price) / price_range, 1.0)
            
            # Volume confirmation for breakouts
            if breakout_type != 'none':
                recent_volume = m15_data['volume'].tail(5).mean()
                avg_volume = m15_data['volume'].tail(20).mean()
                volume_confirmation = recent_volume > avg_volume * self.parameters['volume_threshold']
            else:
                volume_confirmation = False
            
            # Bollinger Band breakouts
            latest = m15_data.iloc[-1]
            bb_upper = latest['bb_upper']
            bb_lower = latest['bb_lower']
            
            bb_breakout = 'none'
            if current_price > bb_upper:
                bb_breakout = 'upper_band'
            elif current_price < bb_lower:
                bb_breakout = 'lower_band'
            
            return {
                'type': breakout_type,
                'strength': breakout_strength,
                'resistance_level': resistance_level,
                'support_level': support_level,
                'volume_confirmation': volume_confirmation,
                'bb_breakout': bb_breakout
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing breakouts: {e}")
            return {'type': 'none', 'strength': 0, 'volume_confirmation': False}
    
    def _analyze_momentum(self, m15_data: pd.DataFrame, h1_data: pd.DataFrame) -> Dict:
        """Analyze momentum indicators"""
        try:
            m15_latest = m15_data.iloc[-1]
            h1_latest = h1_data.iloc[-1]
            
            # RSI analysis
            rsi_m15 = m15_latest['rsi']
            rsi_h1 = h1_latest['rsi']
            
            rsi_signal = 'neutral'
            if rsi_m15 < self.parameters['rsi_oversold'] and rsi_h1 < 50:
                rsi_signal = 'oversold_buy'
            elif rsi_m15 > self.parameters['rsi_overbought'] and rsi_h1 > 50:
                rsi_signal = 'overbought_sell'
            
            # MACD analysis
            macd_m15 = m15_latest['macd']
            macd_signal_m15 = m15_latest['macd_signal']
            macd_histogram = macd_m15 - macd_signal_m15
            
            macd_trend = 'bullish' if macd_m15 > macd_signal_m15 else 'bearish'
            macd_momentum = 'increasing' if macd_histogram > 0 else 'decreasing'
            
            # Stochastic analysis
            stoch_k = m15_latest.get('stoch_k', 50)
            stoch_d = m15_latest.get('stoch_d', 50)
            
            stoch_signal = 'neutral'
            if stoch_k < 20 and stoch_d < 20:
                stoch_signal = 'oversold'
            elif stoch_k > 80 and stoch_d > 80:
                stoch_signal = 'overbought'
            
            # Momentum alignment score
            bullish_signals = 0
            bearish_signals = 0
            
            if rsi_signal == 'oversold_buy':
                bullish_signals += 1
            elif rsi_signal == 'overbought_sell':
                bearish_signals += 1
            
            if macd_trend == 'bullish':
                bullish_signals += 1
            else:
                bearish_signals += 1
            
            if stoch_signal == 'oversold':
                bullish_signals += 1
            elif stoch_signal == 'overbought':
                bearish_signals += 1
            
            momentum_direction = 'bullish' if bullish_signals > bearish_signals else 'bearish'
            momentum_strength = max(bullish_signals, bearish_signals) / 3
            
            return {
                'direction': momentum_direction,
                'strength': momentum_strength,
                'rsi_signal': rsi_signal,
                'macd_trend': macd_trend,
                'stoch_signal': stoch_signal,
                'rsi_m15': rsi_m15,
                'rsi_h1': rsi_h1
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing momentum: {e}")
            return {'direction': 'neutral', 'strength': 0}
    
    def _analyze_volume_profile(self, m15_data: pd.DataFrame) -> Dict:
        """Analyze volume patterns"""
        try:
            # Volume moving average
            volume_ma = m15_data['volume'].rolling(
                window=self.parameters['volume_ma_period']
            ).mean()
            
            current_volume = m15_data['volume'].iloc[-1]
            avg_volume = volume_ma.iloc[-1]
            
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Volume trend
            recent_volume_trend = volume_ma.tail(5).pct_change().mean()
            
            # Volume confirmation
            confirmation = volume_ratio >= self.parameters['volume_threshold']
            
            # Volume pattern analysis
            if volume_ratio > 2.0:
                pattern = 'spike'
            elif volume_ratio > 1.5:
                pattern = 'high'
            elif volume_ratio < 0.5:
                pattern = 'low'
            else:
                pattern = 'normal'
            
            return {
                'ratio': volume_ratio,
                'trend': recent_volume_trend,
                'confirmation': confirmation,
                'pattern': pattern,
                'current': current_volume,
                'average': avg_volume
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing volume: {e}")
            return {'ratio': 1, 'confirmation': False, 'pattern': 'normal'}

    def _combine_day_trading_signals(self, trend_analysis: Dict, breakout_analysis: Dict,
                                   momentum_analysis: Dict, volume_analysis: Dict,
                                   market_structure: Dict, current_price: float) -> Dict:
        """Combine all day trading signals"""
        try:
            buy_score = 0
            sell_score = 0
            reasons = []

            # Trend analysis weight: 30%
            trend_weight = 0.3
            if trend_analysis['overall_direction'] == 'BULLISH':
                buy_score += trend_analysis['alignment_strength'] * trend_weight
                reasons.append(f"Bullish trend alignment ({trend_analysis['alignment_strength']:.2f})")
            elif trend_analysis['overall_direction'] == 'BEARISH':
                sell_score += trend_analysis['alignment_strength'] * trend_weight
                reasons.append(f"Bearish trend alignment ({trend_analysis['alignment_strength']:.2f})")

            # Breakout analysis weight: 25%
            breakout_weight = 0.25
            if breakout_analysis['type'] == 'resistance_breakout':
                buy_score += breakout_analysis['strength'] * breakout_weight
                if breakout_analysis['volume_confirmation']:
                    buy_score += 0.1  # Volume bonus
                reasons.append("Resistance breakout")
            elif breakout_analysis['type'] == 'support_breakdown':
                sell_score += breakout_analysis['strength'] * breakout_weight
                if breakout_analysis['volume_confirmation']:
                    sell_score += 0.1  # Volume bonus
                reasons.append("Support breakdown")

            # Momentum analysis weight: 25%
            momentum_weight = 0.25
            if momentum_analysis['direction'] == 'bullish':
                buy_score += momentum_analysis['strength'] * momentum_weight
                reasons.append(f"Bullish momentum ({momentum_analysis['strength']:.2f})")
            elif momentum_analysis['direction'] == 'bearish':
                sell_score += momentum_analysis['strength'] * momentum_weight
                reasons.append(f"Bearish momentum ({momentum_analysis['strength']:.2f})")

            # Volume confirmation weight: 10%
            volume_weight = 0.1
            if volume_analysis['confirmation']:
                buy_score += volume_weight
                sell_score += volume_weight
                reasons.append("Volume confirmation")

            # Market structure weight: 10%
            structure_weight = 0.1
            if market_structure['trend_structure'] == 'UPTREND':
                buy_score += structure_weight
                reasons.append("Uptrend structure")
            elif market_structure['trend_structure'] == 'DOWNTREND':
                sell_score += structure_weight
                reasons.append("Downtrend structure")

            # Determine final signal
            min_confidence = self.parameters['min_confidence']

            if buy_score > sell_score and buy_score >= min_confidence:
                direction = 'BUY'
                confidence = min(buy_score, 1.0)
            elif sell_score > buy_score and sell_score >= min_confidence:
                direction = 'SELL'
                confidence = min(sell_score, 1.0)
            else:
                direction = 'HOLD'
                confidence = max(buy_score, sell_score)

            reasoning = f"Day trading signal: {direction} (confidence: {confidence:.2f}). " + \
                       f"Factors: {', '.join(reasons[:4])}"

            return {
                'direction': direction,
                'confidence': confidence,
                'reasoning': reasoning,
                'buy_score': buy_score,
                'sell_score': sell_score
            }

        except Exception as e:
            self.logger.error(f"Error combining day trading signals: {e}")
            return {
                'direction': 'HOLD',
                'confidence': 0.0,
                'reasoning': f"Signal combination error: {str(e)}"
            }

    def _calculate_stop_loss(self, entry_price: float, direction: str, atr: float) -> float:
        """Calculate stop loss based on ATR"""
        multiplier = self.parameters['stop_loss_atr_multiplier']

        if direction == 'BUY':
            return entry_price - (atr * multiplier)
        else:  # SELL
            return entry_price + (atr * multiplier)

    def _calculate_take_profit(self, entry_price: float, direction: str, atr: float) -> float:
        """Calculate take profit based on ATR"""
        multiplier = self.parameters['take_profit_atr_multiplier']

        if direction == 'BUY':
            return entry_price + (atr * multiplier)
        else:  # SELL
            return entry_price - (atr * multiplier)

    def _create_hold_signal(self, market_data: Dict, reason: str = "No trading opportunity") -> Signal:
        """Create a HOLD signal"""
        return Signal(
            timestamp=datetime.now(),
            instrument=market_data.get('instrument', 'UNKNOWN'),
            signal_type='HOLD',
            confidence=0.0,
            entry_price=market_data.get('current_price', {}).get('mid', 0),
            strategy_name=self.name,
            timeframe="M15",
            reasoning=reason
        )

    def _custom_trade_validation(self, signal: Signal, current_time: datetime = None) -> bool:
        """
        Custom trade validation for day trading

        Args:
            signal: Trading signal
            current_time: Current timestamp

        Returns:
            True if signal passes day trading validation
        """
        try:
            # Day trading specific validations

            # 1. Check risk/reward ratio
            if signal.stop_loss and signal.take_profit:
                stop_distance = abs(signal.entry_price - signal.stop_loss)
                profit_distance = abs(signal.entry_price - signal.take_profit)

                # Risk/reward ratio should be at least 1:1.5 for day trading
                if profit_distance / stop_distance < 1.5:
                    return False

            # 2. Time-based restrictions
            if current_time:
                hour = current_time.hour

                # Avoid trading during major news events (simplified)
                high_impact_hours = [8, 10, 14, 16]  # Major news times UTC
                if hour in high_impact_hours:
                    minute = current_time.minute
                    if 25 <= minute <= 35:  # 10 minutes around news
                        return False

                # Avoid trading during low liquidity periods
                if hour < 7 or hour > 20:  # Outside main trading sessions
                    return False

            # 3. Signal age validation (day trading signals can be older than scalping)
            if signal.timestamp and current_time:
                signal_age = (current_time - signal.timestamp).total_seconds()
                if signal_age > 900:  # Signal older than 15 minutes
                    return False

            # 4. Minimum confidence for day trading
            if signal.confidence < 0.65:
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error in day trading validation: {e}")
            return False

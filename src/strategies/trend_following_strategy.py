"""
Trend Following Strategy
Long-term trend identification and following strategy
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List
from .base_strategy import BaseStrategy, Signal

class TrendFollowingStrategy(BaseStrategy):
    """
    Advanced trend following strategy using multiple trend indicators
    
    Features:
    - Multi-timeframe trend analysis (H1, H4, D1)
    - Trend strength measurement
    - Pullback entry opportunities
    - Dynamic stop losses
    - Trend continuation patterns
    """
    
    def __init__(self, parameters: Dict = None):
        default_params = {
            'trend_ema_periods': [21, 50, 100],
            'adx_period': 14,
            'adx_threshold': 25,
            'rsi_period': 14,
            'atr_period': 14,
            'pullback_rsi_oversold': 40,
            'pullback_rsi_overbought': 60,
            'trend_strength_threshold': 0.002,
            'stop_loss_atr_multiplier': 3.0,
            'take_profit_atr_multiplier': 6.0,
            'trailing_stop_atr_multiplier': 2.5,
            'min_confidence': 0.7,
            'volume_confirmation_threshold': 1.2
        }
        
        if parameters:
            default_params.update(parameters)
        
        super().__init__(
            name="TrendFollowing",
            timeframes=['H1', 'H4', 'D1'],
            parameters=default_params
        )
    
    def get_required_indicators(self) -> List[str]:
        """Get required technical indicators"""
        return [
            'ema_21', 'ema_50', 'ema_100', 'adx', 'plus_di', 'minus_di',
            'rsi', 'atr', 'volume', 'macd', 'macd_signal', 'parabolic_sar'
        ]
    
    def analyze(self, market_data: Dict) -> Signal:
        """
        Analyze market for trend following opportunities
        
        Args:
            market_data: Market data with H1, H4, and D1 timeframes
            
        Returns:
            Trend following signal
        """
        try:
            if not self.validate_market_data(market_data):
                return self._create_hold_signal(market_data)
            
            # Get current market info
            instrument = market_data['instrument']
            current_price = market_data['current_price']['mid']
            timestamp = datetime.now()
            
            # Get timeframe data
            h1_data = market_data['H1']
            h4_data = market_data['H4']
            d1_data = market_data['D1']
            
            # Check if we have enough data
            if len(h1_data) < 150 or len(h4_data) < 150 or len(d1_data) < 100:
                return self._create_hold_signal(market_data)
            
            # Get latest values
            h1_latest = h1_data.iloc[-1]
            h4_latest = h4_data.iloc[-1]
            d1_latest = d1_data.iloc[-1]
            
            # Calculate ATR for stop loss/take profit
            atr = h1_latest['atr']
            
            # Analyze trend across all timeframes
            trend_analysis = self._analyze_trend_alignment(h1_data, h4_data, d1_data)
            
            # Analyze trend strength
            trend_strength = self._analyze_trend_strength(h1_data, h4_data, d1_data)
            
            # Look for pullback opportunities
            pullback_analysis = self._analyze_pullback_opportunities(h1_data, current_price)
            
            # Analyze momentum confirmation
            momentum_confirmation = self._analyze_momentum_confirmation(h1_data, h4_data)
            
            # Volume analysis
            volume_analysis = self._analyze_volume_trend(h1_data)
            
            # Combine all analyses
            final_signal = self._combine_trend_signals(
                trend_analysis, trend_strength, pullback_analysis,
                momentum_confirmation, volume_analysis, current_price
            )
            
            # Create signal object
            signal = Signal(
                timestamp=timestamp,
                instrument=instrument,
                signal_type=final_signal['direction'],
                confidence=final_signal['confidence'],
                entry_price=current_price,
                stop_loss=self._calculate_dynamic_stop_loss(
                    current_price, final_signal['direction'], atr, trend_analysis
                ),
                take_profit=self._calculate_take_profit(current_price, final_signal['direction'], atr),
                strategy_name=self.name,
                timeframe="H1",
                reasoning=final_signal['reasoning'],
                metadata={
                    'atr': atr,
                    'trend_strength': trend_strength.get('overall_strength', 0),
                    'trend_direction': trend_analysis.get('overall_direction', 'MIXED'),
                    'pullback_opportunity': pullback_analysis.get('opportunity', False),
                    'momentum_confirmation': momentum_confirmation.get('confirmed', False)
                }
            )
            
            self.last_signal = signal
            self.last_analysis_time = timestamp
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error in trend following analysis: {e}")
            return self._create_hold_signal(market_data, f"Analysis error: {str(e)}")
    
    def _analyze_trend_alignment(self, h1_data: pd.DataFrame, h4_data: pd.DataFrame, 
                               d1_data: pd.DataFrame) -> Dict:
        """Analyze trend alignment across timeframes"""
        try:
            trends = {}
            
            # Analyze each timeframe
            for name, data in [('H1', h1_data), ('H4', h4_data), ('D1', d1_data)]:
                latest = data.iloc[-1]
                
                # EMA alignment
                ema_21 = latest['ema_21']
                ema_50 = latest['ema_50']
                ema_100 = latest['ema_100']
                current_price = latest['close']
                
                # Determine trend direction
                if current_price > ema_21 > ema_50 > ema_100:
                    direction = 'STRONG_BULLISH'
                    strength = 1.0
                elif current_price > ema_21 > ema_50:
                    direction = 'BULLISH'
                    strength = 0.8
                elif current_price > ema_21:
                    direction = 'WEAK_BULLISH'
                    strength = 0.6
                elif current_price < ema_21 < ema_50 < ema_100:
                    direction = 'STRONG_BEARISH'
                    strength = 1.0
                elif current_price < ema_21 < ema_50:
                    direction = 'BEARISH'
                    strength = 0.8
                elif current_price < ema_21:
                    direction = 'WEAK_BEARISH'
                    strength = 0.6
                else:
                    direction = 'SIDEWAYS'
                    strength = 0.3
                
                # ADX confirmation
                adx = latest.get('adx', 20)
                adx_strength = min(adx / 50, 1.0)  # Normalize ADX
                
                trends[name] = {
                    'direction': direction,
                    'strength': strength * adx_strength,
                    'adx': adx,
                    'ema_alignment': direction
                }
            
            # Calculate overall trend alignment
            bullish_timeframes = sum(1 for tf in trends.values() 
                                   if 'BULLISH' in tf['direction'])
            bearish_timeframes = sum(1 for tf in trends.values() 
                                   if 'BEARISH' in tf['direction'])
            
            if bullish_timeframes >= 2:
                overall_direction = 'BULLISH'
                alignment_score = bullish_timeframes / 3
            elif bearish_timeframes >= 2:
                overall_direction = 'BEARISH'
                alignment_score = bearish_timeframes / 3
            else:
                overall_direction = 'MIXED'
                alignment_score = 0.5
            
            # Calculate weighted strength
            weights = {'D1': 0.5, 'H4': 0.3, 'H1': 0.2}
            weighted_strength = sum(trends[tf]['strength'] * weights[tf] 
                                  for tf in trends.keys())
            
            return {
                'overall_direction': overall_direction,
                'alignment_score': alignment_score,
                'weighted_strength': weighted_strength,
                'timeframe_trends': trends
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing trend alignment: {e}")
            return {'overall_direction': 'MIXED', 'alignment_score': 0.5}
    
    def _analyze_trend_strength(self, h1_data: pd.DataFrame, h4_data: pd.DataFrame, 
                              d1_data: pd.DataFrame) -> Dict:
        """Analyze overall trend strength"""
        try:
            strength_indicators = {}
            
            # ADX analysis across timeframes
            adx_values = []
            for name, data in [('H1', h1_data), ('H4', h4_data), ('D1', d1_data)]:
                latest = data.iloc[-1]
                adx = latest.get('adx', 20)
                adx_values.append(adx)
                
                # Trend strength classification
                if adx > 40:
                    strength = 'VERY_STRONG'
                elif adx > 25:
                    strength = 'STRONG'
                elif adx > 15:
                    strength = 'MODERATE'
                else:
                    strength = 'WEAK'
                
                strength_indicators[f'{name}_adx'] = {
                    'value': adx,
                    'strength': strength
                }
            
            # Average ADX
            avg_adx = np.mean(adx_values)
            
            # Price momentum analysis
            h1_latest = h1_data.iloc[-1]
            h1_prev = h1_data.iloc[-20]  # 20 periods ago
            
            price_change = (h1_latest['close'] - h1_prev['close']) / h1_prev['close']
            momentum_strength = min(abs(price_change) * 100, 1.0)  # Normalize
            
            # MACD trend strength
            macd = h1_latest.get('macd', 0)
            macd_signal = h1_latest.get('macd_signal', 0)
            macd_strength = abs(macd - macd_signal)
            
            # Overall strength score
            overall_strength = (avg_adx / 50 + momentum_strength + 
                              min(macd_strength * 1000, 1.0)) / 3
            
            return {
                'overall_strength': min(overall_strength, 1.0),
                'avg_adx': avg_adx,
                'momentum_strength': momentum_strength,
                'macd_strength': macd_strength,
                'strength_indicators': strength_indicators
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing trend strength: {e}")
            return {'overall_strength': 0.5, 'avg_adx': 20}
    
    def _analyze_pullback_opportunities(self, h1_data: pd.DataFrame, current_price: float) -> Dict:
        """Analyze pullback entry opportunities"""
        try:
            latest = h1_data.iloc[-1]
            
            # RSI pullback analysis
            rsi = latest['rsi']
            
            # Parabolic SAR analysis
            sar = latest.get('parabolic_sar', current_price)
            
            # EMA support/resistance
            ema_21 = latest['ema_21']
            ema_50 = latest['ema_50']
            
            # Determine if we're in a pullback
            pullback_opportunity = False
            pullback_type = 'none'
            
            # Bullish pullback (price pulls back to support in uptrend)
            if (current_price > ema_50 and  # Overall uptrend
                rsi < self.parameters['pullback_rsi_oversold'] and  # Oversold
                current_price > sar):  # Above SAR
                pullback_opportunity = True
                pullback_type = 'bullish_pullback'
            
            # Bearish pullback (price pulls back to resistance in downtrend)
            elif (current_price < ema_50 and  # Overall downtrend
                  rsi > self.parameters['pullback_rsi_overbought'] and  # Overbought
                  current_price < sar):  # Below SAR
                pullback_opportunity = True
                pullback_type = 'bearish_pullback'
            
            # Support/resistance levels
            recent_highs = h1_data['high'].tail(20).max()
            recent_lows = h1_data['low'].tail(20).min()
            
            # Distance to key levels
            distance_to_high = abs(current_price - recent_highs) / recent_highs
            distance_to_low = abs(current_price - recent_lows) / recent_lows
            
            return {
                'opportunity': pullback_opportunity,
                'type': pullback_type,
                'rsi': rsi,
                'sar': sar,
                'distance_to_high': distance_to_high,
                'distance_to_low': distance_to_low,
                'support_level': ema_21 if current_price > ema_21 else ema_50,
                'resistance_level': recent_highs if pullback_type == 'bullish_pullback' else ema_21
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing pullback opportunities: {e}")
            return {'opportunity': False, 'type': 'none'}
    
    def _analyze_momentum_confirmation(self, h1_data: pd.DataFrame, h4_data: pd.DataFrame) -> Dict:
        """Analyze momentum confirmation signals"""
        try:
            h1_latest = h1_data.iloc[-1]
            h4_latest = h4_data.iloc[-1]
            
            # MACD confirmation
            h1_macd = h1_latest.get('macd', 0)
            h1_macd_signal = h1_latest.get('macd_signal', 0)
            h4_macd = h4_latest.get('macd', 0)
            h4_macd_signal = h4_latest.get('macd_signal', 0)
            
            macd_bullish = h1_macd > h1_macd_signal and h4_macd > h4_macd_signal
            macd_bearish = h1_macd < h1_macd_signal and h4_macd < h4_macd_signal
            
            # ADX momentum
            h1_adx = h1_latest.get('adx', 20)
            h4_adx = h4_latest.get('adx', 20)
            
            adx_strong = h1_adx > self.parameters['adx_threshold'] and h4_adx > self.parameters['adx_threshold']
            
            # Plus/Minus DI analysis
            h1_plus_di = h1_latest.get('plus_di', 25)
            h1_minus_di = h1_latest.get('minus_di', 25)
            
            di_bullish = h1_plus_di > h1_minus_di
            di_bearish = h1_minus_di > h1_plus_di
            
            # Overall momentum confirmation
            if macd_bullish and di_bullish and adx_strong:
                confirmation = 'STRONG_BULLISH'
                confirmed = True
            elif macd_bearish and di_bearish and adx_strong:
                confirmation = 'STRONG_BEARISH'
                confirmed = True
            elif macd_bullish and di_bullish:
                confirmation = 'BULLISH'
                confirmed = True
            elif macd_bearish and di_bearish:
                confirmation = 'BEARISH'
                confirmed = True
            else:
                confirmation = 'MIXED'
                confirmed = False
            
            return {
                'confirmed': confirmed,
                'type': confirmation,
                'macd_bullish': macd_bullish,
                'macd_bearish': macd_bearish,
                'adx_strong': adx_strong,
                'di_bullish': di_bullish,
                'di_bearish': di_bearish
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing momentum confirmation: {e}")
            return {'confirmed': False, 'type': 'MIXED'}
    
    def _analyze_volume_trend(self, h1_data: pd.DataFrame) -> Dict:
        """Analyze volume trend confirmation"""
        try:
            # Volume moving average
            volume_ma = h1_data['volume'].rolling(window=20).mean()
            current_volume = h1_data['volume'].iloc[-1]
            avg_volume = volume_ma.iloc[-1]
            
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # Volume trend
            volume_trend = volume_ma.tail(10).pct_change().mean()
            
            # Volume confirmation
            confirmation = volume_ratio >= self.parameters['volume_confirmation_threshold']
            
            return {
                'ratio': volume_ratio,
                'trend': volume_trend,
                'confirmation': confirmation,
                'increasing': volume_trend > 0
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing volume trend: {e}")
            return {'ratio': 1, 'confirmation': False, 'increasing': False}

    def _combine_trend_signals(self, trend_analysis: Dict, trend_strength: Dict,
                             pullback_analysis: Dict, momentum_confirmation: Dict,
                             volume_analysis: Dict, current_price: float) -> Dict:
        """Combine all trend following signals"""
        try:
            buy_score = 0
            sell_score = 0
            reasons = []

            # Trend alignment weight: 40%
            trend_weight = 0.4
            if trend_analysis['overall_direction'] == 'BULLISH':
                buy_score += trend_analysis['alignment_score'] * trend_weight
                reasons.append(f"Bullish trend alignment ({trend_analysis['alignment_score']:.2f})")
            elif trend_analysis['overall_direction'] == 'BEARISH':
                sell_score += trend_analysis['alignment_score'] * trend_weight
                reasons.append(f"Bearish trend alignment ({trend_analysis['alignment_score']:.2f})")

            # Trend strength weight: 25%
            strength_weight = 0.25
            strength_score = trend_strength['overall_strength'] * strength_weight

            if trend_analysis['overall_direction'] == 'BULLISH':
                buy_score += strength_score
            elif trend_analysis['overall_direction'] == 'BEARISH':
                sell_score += strength_score

            reasons.append(f"Trend strength: {trend_strength['overall_strength']:.2f}")

            # Pullback opportunity weight: 20%
            pullback_weight = 0.2
            if pullback_analysis['opportunity']:
                if pullback_analysis['type'] == 'bullish_pullback':
                    buy_score += pullback_weight
                    reasons.append("Bullish pullback opportunity")
                elif pullback_analysis['type'] == 'bearish_pullback':
                    sell_score += pullback_weight
                    reasons.append("Bearish pullback opportunity")

            # Momentum confirmation weight: 10%
            momentum_weight = 0.1
            if momentum_confirmation['confirmed']:
                if 'BULLISH' in momentum_confirmation['type']:
                    buy_score += momentum_weight
                    reasons.append("Bullish momentum confirmed")
                elif 'BEARISH' in momentum_confirmation['type']:
                    sell_score += momentum_weight
                    reasons.append("Bearish momentum confirmed")

            # Volume confirmation weight: 5%
            volume_weight = 0.05
            if volume_analysis['confirmation']:
                buy_score += volume_weight
                sell_score += volume_weight
                reasons.append("Volume confirmation")

            # Determine final signal
            min_confidence = self.parameters['min_confidence']

            if buy_score > sell_score and buy_score >= min_confidence:
                direction = 'BUY'
                confidence = min(buy_score, 1.0)
            elif sell_score > buy_score and sell_score >= min_confidence:
                direction = 'SELL'
                confidence = min(sell_score, 1.0)
            else:
                direction = 'HOLD'
                confidence = max(buy_score, sell_score)

            reasoning = f"Trend following signal: {direction} (confidence: {confidence:.2f}). " + \
                       f"Factors: {', '.join(reasons[:3])}"

            return {
                'direction': direction,
                'confidence': confidence,
                'reasoning': reasoning,
                'buy_score': buy_score,
                'sell_score': sell_score
            }

        except Exception as e:
            self.logger.error(f"Error combining trend signals: {e}")
            return {
                'direction': 'HOLD',
                'confidence': 0.0,
                'reasoning': f"Signal combination error: {str(e)}"
            }

    def _calculate_dynamic_stop_loss(self, entry_price: float, direction: str,
                                   atr: float, trend_analysis: Dict) -> float:
        """Calculate dynamic stop loss based on trend strength"""
        base_multiplier = self.parameters['stop_loss_atr_multiplier']

        # Adjust multiplier based on trend strength
        trend_strength = trend_analysis.get('weighted_strength', 0.5)

        # Stronger trends can have wider stops
        if trend_strength > 0.8:
            multiplier = base_multiplier * 1.2
        elif trend_strength > 0.6:
            multiplier = base_multiplier
        else:
            multiplier = base_multiplier * 0.8

        if direction == 'BUY':
            return entry_price - (atr * multiplier)
        else:  # SELL
            return entry_price + (atr * multiplier)

    def _calculate_take_profit(self, entry_price: float, direction: str, atr: float) -> float:
        """Calculate take profit based on ATR"""
        multiplier = self.parameters['take_profit_atr_multiplier']

        if direction == 'BUY':
            return entry_price + (atr * multiplier)
        else:  # SELL
            return entry_price - (atr * multiplier)

    def _create_hold_signal(self, market_data: Dict, reason: str = "No trading opportunity") -> Signal:
        """Create a HOLD signal"""
        return Signal(
            timestamp=datetime.now(),
            instrument=market_data.get('instrument', 'UNKNOWN'),
            signal_type='HOLD',
            confidence=0.0,
            entry_price=market_data.get('current_price', {}).get('mid', 0),
            strategy_name=self.name,
            timeframe="H1",
            reasoning=reason
        )

    def _custom_trade_validation(self, signal: Signal, current_time: datetime = None) -> bool:
        """
        Custom trade validation for trend following

        Args:
            signal: Trading signal
            current_time: Current timestamp

        Returns:
            True if signal passes trend following validation
        """
        try:
            # Trend following specific validations

            # 1. Check risk/reward ratio (trend following can have wider ratios)
            if signal.stop_loss and signal.take_profit:
                stop_distance = abs(signal.entry_price - signal.stop_loss)
                profit_distance = abs(signal.entry_price - signal.take_profit)

                # Risk/reward ratio should be at least 1:2 for trend following
                if profit_distance / stop_distance < 2.0:
                    return False

            # 2. Time-based restrictions (less restrictive than scalping)
            if current_time:
                hour = current_time.hour

                # Only avoid major news events
                if hour in [8, 14, 16]:  # Major news times UTC
                    minute = current_time.minute
                    if 25 <= minute <= 35:  # 10 minutes around news
                        return False

            # 3. Signal age validation (trend signals can be older)
            if signal.timestamp and current_time:
                signal_age = (current_time - signal.timestamp).total_seconds()
                if signal_age > 3600:  # Signal older than 1 hour
                    return False

            # 4. Minimum confidence for trend following
            if signal.confidence < 0.7:
                return False

            return True

        except Exception as e:
            self.logger.error(f"Error in trend following validation: {e}")
            return False

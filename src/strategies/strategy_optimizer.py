"""
Strategy Optimization Engine
Advanced optimization system for finding optimal strategy parameters and combinations
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import itertools
from scipy.optimize import minimize, differential_evolution, basinhopping
from sklearn.model_selection import ParameterGrid
import warnings
warnings.filterwarnings('ignore')

from ..backtesting.backtest_engine import AdvancedBacktester, BacktestResults

@dataclass
class OptimizationConfig:
    """Configuration for strategy optimization"""
    optimization_method: str = "grid_search"  # grid_search, random_search, genetic, bayesian
    objective_function: str = "sharpe_ratio"  # sharpe_ratio, total_return, calmar_ratio, custom
    max_iterations: int = 100
    population_size: int = 20
    convergence_threshold: float = 1e-6
    parallel_workers: int = 4
    validation_split: float = 0.3
    walk_forward_periods: int = 5
    min_trades_threshold: int = 30
    max_drawdown_limit: float = 25.0
    min_sharpe_threshold: float = 0.5

@dataclass
class OptimizationResult:
    """Result of strategy optimization"""
    strategy_name: str
    original_params: Dict[str, Any]
    optimized_params: Dict[str, Any]
    original_performance: Dict[str, float]
    optimized_performance: Dict[str, float]
    improvement_metrics: Dict[str, float]
    optimization_method: str
    iterations_completed: int
    convergence_achieved: bool
    validation_results: Dict[str, Any]
    parameter_sensitivity: Dict[str, float]

@dataclass
class CombinationOptimization:
    """Result of strategy combination optimization"""
    combination_name: str
    strategies: List[str]
    optimal_weights: List[float]
    rebalancing_frequency: int
    performance_metrics: Dict[str, float]
    risk_metrics: Dict[str, float]
    diversification_benefit: float

class StrategyOptimizer:
    """
    Advanced strategy optimization engine
    
    Features:
    - Multiple optimization algorithms
    - Parameter sensitivity analysis
    - Walk-forward optimization
    - Multi-objective optimization
    - Strategy combination optimization
    - Robust backtesting validation
    - Parallel processing
    """
    
    def __init__(self, backtester: AdvancedBacktester, config: OptimizationConfig = None):
        """
        Initialize the optimizer
        
        Args:
            backtester: Backtesting engine instance
            config: Optimization configuration
        """
        self.backtester = backtester
        self.config = config or OptimizationConfig()
        self.logger = logging.getLogger("StrategyOptimizer")
        
        # Optimization history
        self.optimization_history = []
        self.best_results = {}
        
        # Objective functions
        self.objective_functions = {
            'sharpe_ratio': self._sharpe_objective,
            'total_return': self._return_objective,
            'calmar_ratio': self._calmar_objective,
            'profit_factor': self._profit_factor_objective,
            'win_rate': self._win_rate_objective,
            'custom': self._custom_objective
        }
    
    def optimize_strategy_parameters(self, strategy_class: Any, parameter_ranges: Dict[str, Any],
                                   market_data: Dict[str, pd.DataFrame],
                                   start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None) -> OptimizationResult:
        """
        Optimize parameters for a single strategy
        
        Args:
            strategy_class: Strategy class to optimize
            parameter_ranges: Dictionary of parameter ranges to optimize
            market_data: Market data for backtesting
            start_date: Optimization start date
            end_date: Optimization end date
            
        Returns:
            Optimization results
        """
        try:
            self.logger.info(f"Starting parameter optimization for {strategy_class.__name__}")
            
            # Get original parameters
            original_strategy = strategy_class()
            original_params = original_strategy.parameters.copy()
            
            # Run baseline backtest
            baseline_results = self.backtester.run_backtest(
                market_data, [original_strategy], start_date, end_date
            )
            
            # Choose optimization method
            if self.config.optimization_method == "grid_search":
                optimized_params, best_performance = self._grid_search_optimization(
                    strategy_class, parameter_ranges, market_data, start_date, end_date
                )
            elif self.config.optimization_method == "genetic":
                optimized_params, best_performance = self._genetic_optimization(
                    strategy_class, parameter_ranges, market_data, start_date, end_date
                )
            elif self.config.optimization_method == "bayesian":
                optimized_params, best_performance = self._bayesian_optimization(
                    strategy_class, parameter_ranges, market_data, start_date, end_date
                )
            else:
                optimized_params, best_performance = self._random_search_optimization(
                    strategy_class, parameter_ranges, market_data, start_date, end_date
                )
            
            # Validate results with walk-forward analysis
            validation_results = self._walk_forward_validation(
                strategy_class, optimized_params, market_data, start_date, end_date
            )
            
            # Calculate parameter sensitivity
            sensitivity_analysis = self._parameter_sensitivity_analysis(
                strategy_class, optimized_params, parameter_ranges, market_data, start_date, end_date
            )
            
            # Create optimization result
            result = OptimizationResult(
                strategy_name=strategy_class.__name__,
                original_params=original_params,
                optimized_params=optimized_params,
                original_performance=self._extract_performance_metrics(baseline_results),
                optimized_performance=self._extract_performance_metrics(best_performance),
                improvement_metrics=self._calculate_improvement_metrics(baseline_results, best_performance),
                optimization_method=self.config.optimization_method,
                iterations_completed=len(self.optimization_history),
                convergence_achieved=True,  # Would be determined by optimization algorithm
                validation_results=validation_results,
                parameter_sensitivity=sensitivity_analysis
            )
            
            self.best_results[strategy_class.__name__] = result
            self.logger.info(f"Optimization completed for {strategy_class.__name__}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error optimizing strategy parameters: {e}")
            raise
    
    def _grid_search_optimization(self, strategy_class: Any, parameter_ranges: Dict[str, Any],
                                market_data: Dict[str, pd.DataFrame],
                                start_date: Optional[datetime] = None,
                                end_date: Optional[datetime] = None) -> Tuple[Dict[str, Any], BacktestResults]:
        """Grid search optimization"""
        try:
            # Generate parameter grid
            param_grid = list(ParameterGrid(parameter_ranges))
            
            # Limit grid size if too large
            if len(param_grid) > self.config.max_iterations:
                param_grid = param_grid[:self.config.max_iterations]
                self.logger.warning(f"Grid size limited to {self.config.max_iterations} combinations")
            
            best_params = None
            best_performance = None
            best_score = float('-inf')
            
            # Parallel optimization
            with ThreadPoolExecutor(max_workers=self.config.parallel_workers) as executor:
                # Submit all parameter combinations
                future_to_params = {
                    executor.submit(self._evaluate_parameters, strategy_class, params, market_data, start_date, end_date): params
                    for params in param_grid
                }
                
                # Collect results
                for future in as_completed(future_to_params):
                    params = future_to_params[future]
                    try:
                        results, score = future.result()
                        
                        if score > best_score:
                            best_score = score
                            best_params = params
                            best_performance = results
                            
                        self.optimization_history.append({
                            'params': params,
                            'score': score,
                            'timestamp': datetime.now()
                        })
                        
                    except Exception as e:
                        self.logger.warning(f"Error evaluating parameters {params}: {e}")
            
            return best_params, best_performance
            
        except Exception as e:
            self.logger.error(f"Grid search optimization error: {e}")
            raise
    
    def _genetic_optimization(self, strategy_class: Any, parameter_ranges: Dict[str, Any],
                            market_data: Dict[str, pd.DataFrame],
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None) -> Tuple[Dict[str, Any], BacktestResults]:
        """Genetic algorithm optimization"""
        try:
            # Convert parameter ranges to bounds for scipy
            bounds = []
            param_names = []
            
            for param_name, param_range in parameter_ranges.items():
                if isinstance(param_range, (list, tuple)) and len(param_range) == 2:
                    bounds.append(param_range)
                    param_names.append(param_name)
                elif isinstance(param_range, list):
                    # For discrete values, use min/max
                    bounds.append((min(param_range), max(param_range)))
                    param_names.append(param_name)
            
            def objective_function(x):
                # Convert array to parameter dictionary
                params = {name: value for name, value in zip(param_names, x)}
                try:
                    results, score = self._evaluate_parameters(
                        strategy_class, params, market_data, start_date, end_date
                    )
                    return -score  # Minimize negative score (maximize score)
                except Exception:
                    return float('inf')  # Return high value for invalid parameters
            
            # Run genetic algorithm
            result = differential_evolution(
                objective_function,
                bounds,
                maxiter=self.config.max_iterations,
                popsize=self.config.population_size,
                tol=self.config.convergence_threshold,
                workers=1  # Avoid nested parallelization
            )
            
            # Convert result back to parameters
            best_params = {name: value for name, value in zip(param_names, result.x)}
            
            # Get final performance
            best_performance, _ = self._evaluate_parameters(
                strategy_class, best_params, market_data, start_date, end_date
            )
            
            return best_params, best_performance
            
        except Exception as e:
            self.logger.error(f"Genetic optimization error: {e}")
            raise
    
    def _random_search_optimization(self, strategy_class: Any, parameter_ranges: Dict[str, Any],
                                  market_data: Dict[str, pd.DataFrame],
                                  start_date: Optional[datetime] = None,
                                  end_date: Optional[datetime] = None) -> Tuple[Dict[str, Any], BacktestResults]:
        """Random search optimization"""
        try:
            best_params = None
            best_performance = None
            best_score = float('-inf')
            
            for iteration in range(self.config.max_iterations):
                # Generate random parameters
                random_params = {}
                for param_name, param_range in parameter_ranges.items():
                    if isinstance(param_range, (list, tuple)) and len(param_range) == 2:
                        # Continuous range
                        random_params[param_name] = np.random.uniform(param_range[0], param_range[1])
                    elif isinstance(param_range, list):
                        # Discrete values
                        random_params[param_name] = np.random.choice(param_range)
                
                try:
                    results, score = self._evaluate_parameters(
                        strategy_class, random_params, market_data, start_date, end_date
                    )
                    
                    if score > best_score:
                        best_score = score
                        best_params = random_params
                        best_performance = results
                    
                    self.optimization_history.append({
                        'params': random_params,
                        'score': score,
                        'timestamp': datetime.now()
                    })
                    
                except Exception as e:
                    self.logger.warning(f"Error evaluating random parameters: {e}")
                    continue
            
            return best_params, best_performance
            
        except Exception as e:
            self.logger.error(f"Random search optimization error: {e}")
            raise

    def _bayesian_optimization(self, strategy_class: Any, parameter_ranges: Dict[str, Any],
                             market_data: Dict[str, pd.DataFrame],
                             start_date: Optional[datetime] = None,
                             end_date: Optional[datetime] = None) -> Tuple[Dict[str, Any], BacktestResults]:
        """Bayesian optimization (simplified implementation)"""
        # For now, fall back to random search
        # In a full implementation, this would use libraries like scikit-optimize
        return self._random_search_optimization(strategy_class, parameter_ranges, market_data, start_date, end_date)

    def _evaluate_parameters(self, strategy_class: Any, params: Dict[str, Any],
                           market_data: Dict[str, pd.DataFrame],
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None) -> Tuple[BacktestResults, float]:
        """Evaluate a specific parameter set"""
        try:
            # Create strategy with parameters
            strategy = strategy_class(parameters=params)

            # Run backtest
            results = self.backtester.run_backtest(
                market_data, [strategy], start_date, end_date
            )

            # Apply constraints
            if not self._meets_constraints(results):
                return results, float('-inf')

            # Calculate objective score
            objective_func = self.objective_functions[self.config.objective_function]
            score = objective_func(results)

            return results, score

        except Exception as e:
            self.logger.warning(f"Error evaluating parameters: {e}")
            return None, float('-inf')

    def _meets_constraints(self, results: BacktestResults) -> bool:
        """Check if results meet optimization constraints"""
        if results.total_trades < self.config.min_trades_threshold:
            return False

        if results.max_drawdown_percent > self.config.max_drawdown_limit:
            return False

        if results.sharpe_ratio < self.config.min_sharpe_threshold:
            return False

        return True

    def _sharpe_objective(self, results: BacktestResults) -> float:
        """Sharpe ratio objective function"""
        return results.sharpe_ratio

    def _return_objective(self, results: BacktestResults) -> float:
        """Total return objective function"""
        return results.total_pnl

    def _calmar_objective(self, results: BacktestResults) -> float:
        """Calmar ratio objective function"""
        return results.calmar_ratio

    def _profit_factor_objective(self, results: BacktestResults) -> float:
        """Profit factor objective function"""
        return results.profit_factor

    def _win_rate_objective(self, results: BacktestResults) -> float:
        """Win rate objective function"""
        return results.win_rate

    def _custom_objective(self, results: BacktestResults) -> float:
        """Custom multi-objective function"""
        # Weighted combination of multiple metrics
        sharpe_weight = 0.4
        return_weight = 0.3
        drawdown_weight = 0.2
        win_rate_weight = 0.1

        # Normalize drawdown (lower is better)
        normalized_drawdown = max(0, 1 - (results.max_drawdown_percent / 50))

        score = (
            sharpe_weight * results.sharpe_ratio +
            return_weight * (results.total_pnl / 10000) +  # Normalize return
            drawdown_weight * normalized_drawdown +
            win_rate_weight * results.win_rate
        )

        return score

    def _extract_performance_metrics(self, results: BacktestResults) -> Dict[str, float]:
        """Extract key performance metrics"""
        return {
            'total_return': results.total_pnl,
            'sharpe_ratio': results.sharpe_ratio,
            'max_drawdown': results.max_drawdown_percent,
            'win_rate': results.win_rate,
            'profit_factor': results.profit_factor,
            'total_trades': results.total_trades,
            'calmar_ratio': results.calmar_ratio
        }

    def _calculate_improvement_metrics(self, baseline: BacktestResults, optimized: BacktestResults) -> Dict[str, float]:
        """Calculate improvement metrics"""
        improvements = {}

        metrics = ['total_pnl', 'sharpe_ratio', 'win_rate', 'profit_factor']

        for metric in metrics:
            baseline_val = getattr(baseline, metric, 0)
            optimized_val = getattr(optimized, metric, 0)

            if baseline_val != 0:
                improvement = ((optimized_val - baseline_val) / abs(baseline_val)) * 100
            else:
                improvement = 0.0

            improvements[f'{metric}_improvement_pct'] = improvement

        # Special case for drawdown (lower is better)
        if baseline.max_drawdown_percent != 0:
            dd_improvement = ((baseline.max_drawdown_percent - optimized.max_drawdown_percent) /
                            baseline.max_drawdown_percent) * 100
        else:
            dd_improvement = 0.0

        improvements['max_drawdown_improvement_pct'] = dd_improvement

        return improvements

"""
Strategy Performance Analyzer
Comprehensive analysis and optimization system for trading strategies
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import logging
from scipy import stats
from scipy.optimize import minimize
import itertools

from ..backtesting.backtest_engine import BacktestResults

@dataclass
class StrategyMetrics:
    """Comprehensive strategy performance metrics"""
    name: str
    
    # Basic performance
    total_return: float = 0.0
    annualized_return: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    
    # Risk metrics
    volatility: float = 0.0
    max_drawdown: float = 0.0
    var_95: float = 0.0
    cvar_95: float = 0.0
    
    # Risk-adjusted returns
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # Trade statistics
    total_trades: int = 0
    avg_trade_duration: timedelta = field(default_factory=lambda: timedelta(0))
    avg_win: float = 0.0
    avg_loss: float = 0.0
    
    # Advanced metrics
    kelly_criterion: float = 0.0
    expectancy: float = 0.0
    stability: float = 0.0
    tail_ratio: float = 0.0
    
    # Correlation and diversification
    correlation_with_market: float = 0.0
    beta: float = 0.0
    alpha: float = 0.0

@dataclass
class OptimizationResult:
    """Strategy optimization result"""
    strategy_name: str
    original_params: Dict[str, Any]
    optimized_params: Dict[str, Any]
    original_performance: StrategyMetrics
    optimized_performance: StrategyMetrics
    improvement_pct: float
    optimization_method: str

@dataclass
class CombinationAnalysis:
    """Analysis of strategy combinations"""
    combination_name: str
    strategies: List[str]
    weights: List[float]
    combined_metrics: StrategyMetrics
    diversification_benefit: float
    correlation_matrix: Dict[str, Dict[str, float]]
    risk_reduction: float

class StrategyPerformanceAnalyzer:
    """
    Comprehensive strategy performance analyzer
    
    Features:
    - Detailed performance metrics calculation
    - Strategy optimization recommendations
    - Combination analysis
    - Risk assessment
    - Performance attribution
    - Benchmarking
    """
    
    def __init__(self, risk_free_rate: float = 0.02):
        """
        Initialize the analyzer
        
        Args:
            risk_free_rate: Risk-free rate for Sharpe ratio calculation
        """
        self.risk_free_rate = risk_free_rate
        self.logger = logging.getLogger("StrategyAnalyzer")
        
        # Analysis cache
        self.metrics_cache = {}
        self.correlation_cache = {}
        
    def analyze_strategy_performance(self, backtest_results: Dict[str, BacktestResults], 
                                   market_data: Optional[pd.DataFrame] = None) -> Dict[str, StrategyMetrics]:
        """
        Analyze performance of multiple strategies
        
        Args:
            backtest_results: Dictionary of backtest results by strategy name
            market_data: Market data for benchmark comparison
            
        Returns:
            Dictionary of strategy metrics
        """
        try:
            strategy_metrics = {}
            
            for strategy_name, results in backtest_results.items():
                self.logger.info(f"Analyzing strategy: {strategy_name}")
                
                metrics = self._calculate_comprehensive_metrics(strategy_name, results, market_data)
                strategy_metrics[strategy_name] = metrics
                
                # Cache results
                self.metrics_cache[strategy_name] = metrics
            
            return strategy_metrics
            
        except Exception as e:
            self.logger.error(f"Error analyzing strategy performance: {e}")
            return {}
    
    def _calculate_comprehensive_metrics(self, strategy_name: str, 
                                       results: BacktestResults,
                                       market_data: Optional[pd.DataFrame] = None) -> StrategyMetrics:
        """Calculate comprehensive metrics for a strategy"""
        try:
            metrics = StrategyMetrics(name=strategy_name)
            
            # Basic performance metrics
            metrics.total_return = results.total_pnl
            metrics.win_rate = results.win_rate
            metrics.profit_factor = results.profit_factor
            metrics.total_trades = results.total_trades
            
            # Risk metrics
            metrics.max_drawdown = results.max_drawdown_percent
            metrics.var_95 = results.var_95
            metrics.cvar_95 = results.cvar_95
            
            # Risk-adjusted returns
            metrics.sharpe_ratio = results.sharpe_ratio
            metrics.sortino_ratio = results.sortino_ratio
            metrics.calmar_ratio = results.calmar_ratio
            
            # Trade statistics
            metrics.avg_trade_duration = results.avg_trade_duration
            metrics.avg_win = results.avg_win
            metrics.avg_loss = results.avg_loss
            
            # Advanced metrics
            metrics.kelly_criterion = results.kelly_criterion
            metrics.expectancy = results.expectancy
            
            # Calculate additional metrics
            if results.trade_returns:
                returns = np.array(results.trade_returns)
                
                # Volatility (annualized)
                metrics.volatility = np.std(returns) * np.sqrt(252)  # Assuming daily returns
                
                # Annualized return
                if len(results.equity_curve) > 1:
                    total_days = len(results.equity_curve)
                    metrics.annualized_return = (
                        (results.equity_curve[-1] / results.equity_curve[0]) ** (252 / total_days) - 1
                    )
                
                # Stability (consistency of returns)
                if len(returns) > 1:
                    metrics.stability = self._calculate_stability(returns)
                
                # Tail ratio
                metrics.tail_ratio = self._calculate_tail_ratio(returns)
                
                # Market correlation and beta (if market data provided)
                if market_data is not None:
                    market_correlation = self._calculate_market_correlation(returns, market_data)
                    metrics.correlation_with_market = market_correlation['correlation']
                    metrics.beta = market_correlation['beta']
                    metrics.alpha = market_correlation['alpha']
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating metrics for {strategy_name}: {e}")
            return StrategyMetrics(name=strategy_name)
    
    def _calculate_stability(self, returns: np.ndarray) -> float:
        """Calculate stability of returns (R-squared of linear regression)"""
        try:
            if len(returns) < 2:
                return 0.0
            
            x = np.arange(len(returns))
            cumulative_returns = np.cumsum(returns)
            
            # Linear regression
            slope, intercept, r_value, _, _ = stats.linregress(x, cumulative_returns)
            
            return r_value ** 2  # R-squared
            
        except Exception:
            return 0.0
    
    def _calculate_tail_ratio(self, returns: np.ndarray) -> float:
        """Calculate tail ratio (95th percentile / 5th percentile)"""
        try:
            if len(returns) < 10:
                return 1.0
            
            p95 = np.percentile(returns, 95)
            p5 = np.percentile(returns, 5)
            
            if p5 != 0:
                return abs(p95 / p5)
            else:
                return 1.0
                
        except Exception:
            return 1.0
    
    def _calculate_market_correlation(self, strategy_returns: np.ndarray, 
                                    market_data: pd.DataFrame) -> Dict[str, float]:
        """Calculate correlation, beta, and alpha with market"""
        try:
            # Calculate market returns (assuming 'close' column)
            if 'close' not in market_data.columns:
                return {'correlation': 0.0, 'beta': 0.0, 'alpha': 0.0}
            
            market_returns = market_data['close'].pct_change().dropna().values
            
            # Align lengths
            min_len = min(len(strategy_returns), len(market_returns))
            if min_len < 2:
                return {'correlation': 0.0, 'beta': 0.0, 'alpha': 0.0}
            
            strategy_aligned = strategy_returns[:min_len]
            market_aligned = market_returns[:min_len]
            
            # Correlation
            correlation = np.corrcoef(strategy_aligned, market_aligned)[0, 1]
            if np.isnan(correlation):
                correlation = 0.0
            
            # Beta and Alpha
            market_var = np.var(market_aligned)
            if market_var > 0:
                beta = np.cov(strategy_aligned, market_aligned)[0, 1] / market_var
                alpha = np.mean(strategy_aligned) - beta * np.mean(market_aligned)
            else:
                beta = 0.0
                alpha = np.mean(strategy_aligned)
            
            return {
                'correlation': correlation,
                'beta': beta,
                'alpha': alpha
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating market correlation: {e}")
            return {'correlation': 0.0, 'beta': 0.0, 'alpha': 0.0}
    
    def find_optimal_combinations(self, strategy_metrics: Dict[str, StrategyMetrics], 
                                 max_strategies: int = 3,
                                 min_correlation_threshold: float = 0.8) -> List[CombinationAnalysis]:
        """
        Find optimal strategy combinations
        
        Args:
            strategy_metrics: Dictionary of strategy metrics
            max_strategies: Maximum number of strategies in combination
            min_correlation_threshold: Maximum correlation between strategies
            
        Returns:
            List of optimal combinations
        """
        try:
            if len(strategy_metrics) < 2:
                return []
            
            strategy_names = list(strategy_metrics.keys())
            optimal_combinations = []
            
            # Generate all possible combinations
            for r in range(2, min(max_strategies + 1, len(strategy_names) + 1)):
                for combination in itertools.combinations(strategy_names, r):
                    # Check correlation constraint
                    if self._check_correlation_constraint(combination, min_correlation_threshold):
                        # Analyze this combination
                        analysis = self._analyze_combination(combination, strategy_metrics)
                        if analysis:
                            optimal_combinations.append(analysis)
            
            # Sort by risk-adjusted return
            optimal_combinations.sort(
                key=lambda x: x.combined_metrics.sharpe_ratio, 
                reverse=True
            )
            
            return optimal_combinations[:10]  # Return top 10
            
        except Exception as e:
            self.logger.error(f"Error finding optimal combinations: {e}")
            return []

    def _check_correlation_constraint(self, combination: Tuple[str, ...],
                                    threshold: float) -> bool:
        """Check if strategy combination meets correlation constraint"""
        try:
            # For now, return True - would need actual correlation calculation
            # This would be enhanced with real correlation data
            return True

        except Exception:
            return False

    def _analyze_combination(self, combination: Tuple[str, ...],
                           strategy_metrics: Dict[str, StrategyMetrics]) -> Optional[CombinationAnalysis]:
        """Analyze a specific strategy combination"""
        try:
            strategies = list(combination)
            n_strategies = len(strategies)

            # Equal weights for now (could be optimized)
            weights = [1.0 / n_strategies] * n_strategies

            # Calculate combined metrics
            combined_metrics = self._calculate_combined_metrics(strategies, weights, strategy_metrics)

            # Calculate diversification benefit
            individual_sharpe = [strategy_metrics[s].sharpe_ratio for s in strategies]
            avg_individual_sharpe = np.mean(individual_sharpe)
            diversification_benefit = (combined_metrics.sharpe_ratio - avg_individual_sharpe) / avg_individual_sharpe if avg_individual_sharpe != 0 else 0

            # Risk reduction
            individual_volatility = [strategy_metrics[s].volatility for s in strategies]
            avg_individual_volatility = np.mean(individual_volatility)
            risk_reduction = (avg_individual_volatility - combined_metrics.volatility) / avg_individual_volatility if avg_individual_volatility != 0 else 0

            return CombinationAnalysis(
                combination_name=f"Combo_{'_'.join(strategies)}",
                strategies=strategies,
                weights=weights,
                combined_metrics=combined_metrics,
                diversification_benefit=diversification_benefit,
                correlation_matrix={},  # Would be populated with real data
                risk_reduction=risk_reduction
            )

        except Exception as e:
            self.logger.error(f"Error analyzing combination {combination}: {e}")
            return None

    def _calculate_combined_metrics(self, strategies: List[str], weights: List[float],
                                  strategy_metrics: Dict[str, StrategyMetrics]) -> StrategyMetrics:
        """Calculate metrics for combined strategy portfolio"""
        try:
            combined_name = f"Combined_{'_'.join(strategies)}"
            combined = StrategyMetrics(name=combined_name)

            # Weighted averages for basic metrics
            combined.total_return = sum(w * strategy_metrics[s].total_return for w, s in zip(weights, strategies))
            combined.win_rate = sum(w * strategy_metrics[s].win_rate for w, s in zip(weights, strategies))
            combined.total_trades = sum(strategy_metrics[s].total_trades for s in strategies)

            # Risk metrics (simplified - would need correlation matrix for accurate calculation)
            individual_volatilities = [strategy_metrics[s].volatility for s in strategies]
            # Simplified portfolio volatility (assumes zero correlation)
            combined.volatility = np.sqrt(sum((w * vol) ** 2 for w, vol in zip(weights, individual_volatilities)))

            # Risk-adjusted returns
            if combined.volatility > 0:
                combined.sharpe_ratio = (combined.total_return - self.risk_free_rate) / combined.volatility

            # Max drawdown (worst case)
            combined.max_drawdown = max(strategy_metrics[s].max_drawdown for s in strategies)

            return combined

        except Exception as e:
            self.logger.error(f"Error calculating combined metrics: {e}")
            return StrategyMetrics(name="Error")

    def generate_optimization_recommendations(self, strategy_metrics: Dict[str, StrategyMetrics]) -> Dict[str, Any]:
        """Generate optimization recommendations for strategies"""
        try:
            recommendations = {}

            # Performance ranking
            performance_ranking = sorted(
                strategy_metrics.items(),
                key=lambda x: x[1].sharpe_ratio,
                reverse=True
            )

            recommendations['performance_ranking'] = [
                {
                    'strategy': name,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'total_return': metrics.total_return,
                    'max_drawdown': metrics.max_drawdown,
                    'win_rate': metrics.win_rate
                }
                for name, metrics in performance_ranking
            ]

            # Risk assessment
            high_risk_strategies = [
                name for name, metrics in strategy_metrics.items()
                if metrics.max_drawdown > 20 or metrics.volatility > 0.3
            ]

            recommendations['risk_warnings'] = {
                'high_risk_strategies': high_risk_strategies,
                'recommendations': [
                    f"Consider reducing position size for {name}" for name in high_risk_strategies
                ]
            }

            # Performance improvement suggestions
            improvement_suggestions = []

            for name, metrics in strategy_metrics.items():
                suggestions = []

                if metrics.win_rate < 0.5:
                    suggestions.append("Consider tightening entry criteria to improve win rate")

                if metrics.sharpe_ratio < 1.0:
                    suggestions.append("Risk-adjusted returns could be improved")

                if metrics.max_drawdown > 15:
                    suggestions.append("Implement stronger risk management to reduce drawdown")

                if metrics.total_trades < 50:
                    suggestions.append("Strategy may need more trading opportunities")

                if suggestions:
                    improvement_suggestions.append({
                        'strategy': name,
                        'suggestions': suggestions
                    })

            recommendations['improvement_suggestions'] = improvement_suggestions

            # Best practices
            best_performers = [name for name, _ in performance_ranking[:3]]
            recommendations['best_practices'] = {
                'top_performers': best_performers,
                'common_characteristics': self._analyze_top_performer_characteristics(
                    {name: strategy_metrics[name] for name in best_performers}
                )
            }

            return recommendations

        except Exception as e:
            self.logger.error(f"Error generating recommendations: {e}")
            return {}

    def _analyze_top_performer_characteristics(self, top_performers: Dict[str, StrategyMetrics]) -> List[str]:
        """Analyze common characteristics of top performing strategies"""
        characteristics = []

        if not top_performers:
            return characteristics

        # Average metrics of top performers
        avg_win_rate = np.mean([m.win_rate for m in top_performers.values()])
        avg_sharpe = np.mean([m.sharpe_ratio for m in top_performers.values()])
        avg_drawdown = np.mean([m.max_drawdown for m in top_performers.values()])

        if avg_win_rate > 0.6:
            characteristics.append(f"High win rate (avg: {avg_win_rate:.1%})")

        if avg_sharpe > 1.5:
            characteristics.append(f"Strong risk-adjusted returns (avg Sharpe: {avg_sharpe:.2f})")

        if avg_drawdown < 10:
            characteristics.append(f"Low maximum drawdown (avg: {avg_drawdown:.1f}%)")

        return characteristics

    def generate_performance_report(self, strategy_metrics: Dict[str, StrategyMetrics],
                                  combinations: List[CombinationAnalysis] = None) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': {},
                'individual_strategies': {},
                'combinations': {},
                'recommendations': {}
            }

            # Summary statistics
            if strategy_metrics:
                total_returns = [m.total_return for m in strategy_metrics.values()]
                sharpe_ratios = [m.sharpe_ratio for m in strategy_metrics.values()]
                win_rates = [m.win_rate for m in strategy_metrics.values()]

                report['summary'] = {
                    'total_strategies': len(strategy_metrics),
                    'profitable_strategies': sum(1 for r in total_returns if r > 0),
                    'average_return': np.mean(total_returns),
                    'average_sharpe': np.mean(sharpe_ratios),
                    'average_win_rate': np.mean(win_rates),
                    'best_strategy': max(strategy_metrics.items(), key=lambda x: x[1].sharpe_ratio)[0],
                    'worst_strategy': min(strategy_metrics.items(), key=lambda x: x[1].sharpe_ratio)[0]
                }

            # Individual strategy details
            for name, metrics in strategy_metrics.items():
                report['individual_strategies'][name] = {
                    'total_return': metrics.total_return,
                    'annualized_return': metrics.annualized_return,
                    'sharpe_ratio': metrics.sharpe_ratio,
                    'max_drawdown': metrics.max_drawdown,
                    'win_rate': metrics.win_rate,
                    'total_trades': metrics.total_trades,
                    'volatility': metrics.volatility,
                    'kelly_criterion': metrics.kelly_criterion
                }

            # Combination analysis
            if combinations:
                report['combinations'] = {
                    combo.combination_name: {
                        'strategies': combo.strategies,
                        'weights': combo.weights,
                        'combined_sharpe': combo.combined_metrics.sharpe_ratio,
                        'diversification_benefit': combo.diversification_benefit,
                        'risk_reduction': combo.risk_reduction
                    }
                    for combo in combinations
                }

            # Recommendations
            report['recommendations'] = self.generate_optimization_recommendations(strategy_metrics)

            return report

        except Exception as e:
            self.logger.error(f"Error generating performance report: {e}")
            return {}

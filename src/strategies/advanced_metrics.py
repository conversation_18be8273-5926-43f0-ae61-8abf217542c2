"""
Advanced Strategy Metrics and Reporting
Comprehensive metrics calculation and reporting system for trading strategies
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import logging
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from io import BytesIO
import base64

from ..backtesting.backtest_engine import BacktestResults

@dataclass
class AdvancedMetrics:
    """Advanced performance metrics for strategies"""
    # Basic metrics
    strategy_name: str
    total_return: float = 0.0
    annualized_return: float = 0.0
    volatility: float = 0.0
    
    # Risk-adjusted returns
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    omega_ratio: float = 0.0
    
    # Drawdown analysis
    max_drawdown: float = 0.0
    avg_drawdown: float = 0.0
    drawdown_duration: timedelta = field(default_factory=lambda: timedelta(0))
    recovery_factor: float = 0.0
    
    # Risk metrics
    var_95: float = 0.0
    cvar_95: float = 0.0
    downside_deviation: float = 0.0
    upside_capture: float = 0.0
    downside_capture: float = 0.0
    
    # Trade analysis
    win_rate: float = 0.0
    profit_factor: float = 0.0
    expectancy: float = 0.0
    kelly_criterion: float = 0.0
    
    # Advanced statistics
    skewness: float = 0.0
    kurtosis: float = 0.0
    tail_ratio: float = 0.0
    gain_to_pain_ratio: float = 0.0
    
    # Consistency metrics
    stability: float = 0.0
    consistency_score: float = 0.0
    monthly_win_rate: float = 0.0
    
    # Market correlation
    beta: float = 0.0
    alpha: float = 0.0
    correlation: float = 0.0
    tracking_error: float = 0.0

@dataclass
class CorrelationAnalysis:
    """Correlation analysis between strategies"""
    correlation_matrix: Dict[str, Dict[str, float]] = field(default_factory=dict)
    rolling_correlations: Dict[str, pd.Series] = field(default_factory=dict)
    correlation_stability: Dict[str, float] = field(default_factory=dict)
    diversification_ratio: float = 0.0
    effective_strategies: int = 0

@dataclass
class DrawdownAnalysis:
    """Detailed drawdown analysis"""
    strategy_name: str
    max_drawdown: float = 0.0
    max_drawdown_duration: timedelta = field(default_factory=lambda: timedelta(0))
    avg_drawdown: float = 0.0
    drawdown_frequency: float = 0.0
    recovery_times: List[timedelta] = field(default_factory=list)
    underwater_curve: pd.Series = field(default_factory=pd.Series)
    drawdown_periods: List[Dict] = field(default_factory=list)

class AdvancedMetricsCalculator:
    """
    Advanced metrics calculator for trading strategies
    
    Features:
    - Comprehensive risk-adjusted return metrics
    - Detailed drawdown analysis
    - Correlation and diversification analysis
    - Statistical analysis of returns
    - Performance attribution
    - Risk decomposition
    """
    
    def __init__(self, risk_free_rate: float = 0.02, benchmark_returns: Optional[pd.Series] = None):
        """
        Initialize the metrics calculator
        
        Args:
            risk_free_rate: Risk-free rate for calculations
            benchmark_returns: Benchmark returns for comparison
        """
        self.risk_free_rate = risk_free_rate
        self.benchmark_returns = benchmark_returns
        self.logger = logging.getLogger("AdvancedMetrics")
    
    def calculate_advanced_metrics(self, backtest_results: BacktestResults, 
                                 returns_series: Optional[pd.Series] = None) -> AdvancedMetrics:
        """
        Calculate comprehensive advanced metrics
        
        Args:
            backtest_results: Backtest results
            returns_series: Optional returns series for detailed analysis
            
        Returns:
            Advanced metrics object
        """
        try:
            metrics = AdvancedMetrics(strategy_name=getattr(backtest_results, 'strategy_name', 'Unknown'))
            
            # Basic metrics from backtest results
            metrics.total_return = backtest_results.total_pnl
            metrics.sharpe_ratio = backtest_results.sharpe_ratio
            metrics.max_drawdown = backtest_results.max_drawdown_percent
            metrics.win_rate = backtest_results.win_rate
            metrics.profit_factor = backtest_results.profit_factor
            
            # Calculate advanced metrics if returns series is available
            if returns_series is not None and len(returns_series) > 0:
                self._calculate_return_metrics(metrics, returns_series)
                self._calculate_risk_metrics(metrics, returns_series)
                self._calculate_statistical_metrics(metrics, returns_series)
                self._calculate_consistency_metrics(metrics, returns_series)
                
                if self.benchmark_returns is not None:
                    self._calculate_benchmark_metrics(metrics, returns_series)
            
            # Calculate trade-based metrics
            if backtest_results.trades:
                self._calculate_trade_metrics(metrics, backtest_results)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating advanced metrics: {e}")
            return AdvancedMetrics(strategy_name="Error")
    
    def _calculate_return_metrics(self, metrics: AdvancedMetrics, returns: pd.Series):
        """Calculate return-based metrics"""
        try:
            # Annualized return
            if len(returns) > 0:
                total_return = (1 + returns).prod() - 1
                periods_per_year = 252  # Assuming daily returns
                years = len(returns) / periods_per_year
                metrics.annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
                
                # Volatility
                metrics.volatility = returns.std() * np.sqrt(periods_per_year)
                
                # Sortino ratio
                downside_returns = returns[returns < 0]
                if len(downside_returns) > 0:
                    downside_std = downside_returns.std() * np.sqrt(periods_per_year)
                    metrics.sortino_ratio = (metrics.annualized_return - self.risk_free_rate) / downside_std
                
                # Calmar ratio
                if metrics.max_drawdown != 0:
                    metrics.calmar_ratio = metrics.annualized_return / abs(metrics.max_drawdown / 100)
                
                # Omega ratio
                metrics.omega_ratio = self._calculate_omega_ratio(returns)
                
        except Exception as e:
            self.logger.error(f"Error calculating return metrics: {e}")
    
    def _calculate_risk_metrics(self, metrics: AdvancedMetrics, returns: pd.Series):
        """Calculate risk metrics"""
        try:
            if len(returns) > 0:
                # VaR and CVaR
                metrics.var_95 = np.percentile(returns, 5)
                tail_returns = returns[returns <= metrics.var_95]
                metrics.cvar_95 = tail_returns.mean() if len(tail_returns) > 0 else 0
                
                # Downside deviation
                downside_returns = returns[returns < 0]
                metrics.downside_deviation = downside_returns.std() if len(downside_returns) > 0 else 0
                
                # Tail ratio
                if metrics.var_95 != 0:
                    p95 = np.percentile(returns, 95)
                    metrics.tail_ratio = abs(p95 / metrics.var_95)
                
                # Gain to pain ratio
                positive_returns = returns[returns > 0].sum()
                negative_returns = abs(returns[returns < 0].sum())
                metrics.gain_to_pain_ratio = positive_returns / negative_returns if negative_returns != 0 else 0
                
        except Exception as e:
            self.logger.error(f"Error calculating risk metrics: {e}")
    
    def _calculate_statistical_metrics(self, metrics: AdvancedMetrics, returns: pd.Series):
        """Calculate statistical metrics"""
        try:
            if len(returns) > 1:
                # Skewness and kurtosis
                metrics.skewness = stats.skew(returns)
                metrics.kurtosis = stats.kurtosis(returns)
                
                # Stability (R-squared of linear regression)
                x = np.arange(len(returns))
                cumulative_returns = (1 + returns).cumprod()
                if len(cumulative_returns) > 1:
                    slope, intercept, r_value, _, _ = stats.linregress(x, cumulative_returns)
                    metrics.stability = r_value ** 2
                
        except Exception as e:
            self.logger.error(f"Error calculating statistical metrics: {e}")
    
    def _calculate_consistency_metrics(self, metrics: AdvancedMetrics, returns: pd.Series):
        """Calculate consistency metrics"""
        try:
            if len(returns) > 0:
                # Monthly win rate (if we have enough data)
                if len(returns) >= 30:  # At least 30 periods
                    # Group by months (assuming daily data)
                    monthly_returns = returns.groupby(returns.index.to_period('M')).sum()
                    metrics.monthly_win_rate = (monthly_returns > 0).mean()
                
                # Consistency score (inverse of return volatility)
                if metrics.volatility > 0:
                    metrics.consistency_score = metrics.annualized_return / metrics.volatility
                
        except Exception as e:
            self.logger.error(f"Error calculating consistency metrics: {e}")
    
    def _calculate_benchmark_metrics(self, metrics: AdvancedMetrics, returns: pd.Series):
        """Calculate benchmark-relative metrics"""
        try:
            if self.benchmark_returns is not None and len(returns) > 0:
                # Align returns with benchmark
                aligned_returns, aligned_benchmark = returns.align(self.benchmark_returns, join='inner')
                
                if len(aligned_returns) > 1:
                    # Beta and Alpha
                    covariance = np.cov(aligned_returns, aligned_benchmark)[0, 1]
                    benchmark_variance = np.var(aligned_benchmark)
                    
                    if benchmark_variance > 0:
                        metrics.beta = covariance / benchmark_variance
                        metrics.alpha = metrics.annualized_return - (self.risk_free_rate + 
                                                                   metrics.beta * (aligned_benchmark.mean() * 252 - self.risk_free_rate))
                    
                    # Correlation
                    metrics.correlation = np.corrcoef(aligned_returns, aligned_benchmark)[0, 1]
                    
                    # Tracking error
                    excess_returns = aligned_returns - aligned_benchmark
                    metrics.tracking_error = excess_returns.std() * np.sqrt(252)
                    
                    # Upside/Downside capture
                    up_benchmark = aligned_benchmark[aligned_benchmark > 0]
                    down_benchmark = aligned_benchmark[aligned_benchmark < 0]
                    
                    if len(up_benchmark) > 0:
                        up_strategy = aligned_returns[aligned_benchmark > 0]
                        metrics.upside_capture = up_strategy.mean() / up_benchmark.mean()
                    
                    if len(down_benchmark) > 0:
                        down_strategy = aligned_returns[aligned_benchmark < 0]
                        metrics.downside_capture = down_strategy.mean() / down_benchmark.mean()
                
        except Exception as e:
            self.logger.error(f"Error calculating benchmark metrics: {e}")
    
    def _calculate_trade_metrics(self, metrics: AdvancedMetrics, backtest_results: BacktestResults):
        """Calculate trade-based metrics"""
        try:
            if backtest_results.trades:
                trade_returns = [trade.pnl for trade in backtest_results.trades if trade.pnl is not None]
                
                if trade_returns:
                    # Expectancy
                    metrics.expectancy = np.mean(trade_returns)
                    
                    # Kelly criterion
                    winning_trades = [r for r in trade_returns if r > 0]
                    losing_trades = [r for r in trade_returns if r < 0]
                    
                    if winning_trades and losing_trades:
                        win_rate = len(winning_trades) / len(trade_returns)
                        avg_win = np.mean(winning_trades)
                        avg_loss = abs(np.mean(losing_trades))
                        
                        if avg_loss > 0:
                            win_loss_ratio = avg_win / avg_loss
                            metrics.kelly_criterion = win_rate - (1 - win_rate) / win_loss_ratio
                
        except Exception as e:
            self.logger.error(f"Error calculating trade metrics: {e}")
    
    def _calculate_omega_ratio(self, returns: pd.Series, threshold: float = 0.0) -> float:
        """Calculate Omega ratio"""
        try:
            if len(returns) == 0:
                return 0.0
            
            excess_returns = returns - threshold
            positive_returns = excess_returns[excess_returns > 0].sum()
            negative_returns = abs(excess_returns[excess_returns < 0].sum())
            
            return positive_returns / negative_returns if negative_returns != 0 else 0.0
            
        except Exception:
            return 0.0
    
    def calculate_correlation_analysis(self, strategy_results: Dict[str, BacktestResults]) -> CorrelationAnalysis:
        """Calculate correlation analysis between strategies"""
        try:
            analysis = CorrelationAnalysis()
            
            if len(strategy_results) < 2:
                return analysis
            
            # Extract returns series for each strategy
            returns_data = {}
            for name, results in strategy_results.items():
                if results.trade_returns:
                    returns_data[name] = pd.Series(results.trade_returns)
            
            if len(returns_data) < 2:
                return analysis
            
            # Create correlation matrix
            strategy_names = list(returns_data.keys())
            correlation_matrix = {}
            
            for i, strategy1 in enumerate(strategy_names):
                correlation_matrix[strategy1] = {}
                for j, strategy2 in enumerate(strategy_names):
                    if i == j:
                        correlation_matrix[strategy1][strategy2] = 1.0
                    else:
                        returns1 = returns_data[strategy1]
                        returns2 = returns_data[strategy2]
                        
                        # Align series
                        aligned1, aligned2 = returns1.align(returns2, join='inner')
                        
                        if len(aligned1) > 1:
                            corr = np.corrcoef(aligned1, aligned2)[0, 1]
                            correlation_matrix[strategy1][strategy2] = corr if not np.isnan(corr) else 0.0
                        else:
                            correlation_matrix[strategy1][strategy2] = 0.0
            
            analysis.correlation_matrix = correlation_matrix
            
            # Calculate diversification ratio
            correlations = []
            for i, strategy1 in enumerate(strategy_names):
                for j, strategy2 in enumerate(strategy_names):
                    if i < j:  # Avoid duplicates
                        correlations.append(abs(correlation_matrix[strategy1][strategy2]))
            
            if correlations:
                avg_correlation = np.mean(correlations)
                analysis.diversification_ratio = 1 - avg_correlation
                analysis.effective_strategies = len(strategy_names) * analysis.diversification_ratio
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation analysis: {e}")
            return CorrelationAnalysis()
    
    def calculate_drawdown_analysis(self, equity_curve: pd.Series, strategy_name: str = "Strategy") -> DrawdownAnalysis:
        """Calculate detailed drawdown analysis"""
        try:
            analysis = DrawdownAnalysis(strategy_name=strategy_name)
            
            if len(equity_curve) < 2:
                return analysis
            
            # Calculate running maximum and drawdown
            running_max = equity_curve.expanding().max()
            drawdown = (equity_curve - running_max) / running_max
            
            # Basic drawdown metrics
            analysis.max_drawdown = drawdown.min() * 100  # Convert to percentage
            analysis.avg_drawdown = drawdown[drawdown < 0].mean() * 100 if (drawdown < 0).any() else 0
            
            # Underwater curve
            analysis.underwater_curve = drawdown * 100
            
            # Find drawdown periods
            in_drawdown = drawdown < 0
            drawdown_starts = in_drawdown & ~in_drawdown.shift(1, fill_value=False)
            drawdown_ends = ~in_drawdown & in_drawdown.shift(1, fill_value=False)
            
            # Calculate drawdown periods
            start_indices = drawdown_starts[drawdown_starts].index
            end_indices = drawdown_ends[drawdown_ends].index
            
            drawdown_periods = []
            recovery_times = []
            
            for i, start in enumerate(start_indices):
                # Find corresponding end
                end = None
                for end_candidate in end_indices:
                    if end_candidate > start:
                        end = end_candidate
                        break
                
                if end is None:
                    end = equity_curve.index[-1]  # Still in drawdown
                
                period_drawdown = drawdown.loc[start:end]
                max_dd_in_period = period_drawdown.min() * 100
                duration = end - start
                
                drawdown_periods.append({
                    'start': start,
                    'end': end,
                    'duration': duration,
                    'max_drawdown': max_dd_in_period,
                    'recovered': end in end_indices
                })
                
                if end in end_indices:
                    recovery_times.append(duration)
            
            analysis.drawdown_periods = drawdown_periods
            analysis.recovery_times = recovery_times
            
            # Calculate additional metrics
            if drawdown_periods:
                analysis.max_drawdown_duration = max(period['duration'] for period in drawdown_periods)
                analysis.drawdown_frequency = len(drawdown_periods) / len(equity_curve) * 252  # Annualized
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error calculating drawdown analysis: {e}")
            return DrawdownAnalysis(strategy_name=strategy_name)

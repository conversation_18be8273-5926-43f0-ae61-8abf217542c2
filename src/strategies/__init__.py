"""
Trading Strategies Package

This package contains all trading strategies and ensemble methods
"""

from .base_strategy import BaseStrategy, Signal, StrategyPerformance
from .scalping_strategy import ScalpingStrategy
from .day_trading_strategy import DayTradingStrategy
from .trend_following_strategy import TrendFollowingStrategy
from .strategy_ensemble import StrategyEnsemble, EnsembleMethod

__all__ = [
    'BaseStrategy',
    'Signal',
    'StrategyPerformance',
    'ScalpingStrategy',
    'DayTradingStrategy',
    'TrendFollowingStrategy',
    'StrategyEnsemble',
    'EnsembleMethod'
]

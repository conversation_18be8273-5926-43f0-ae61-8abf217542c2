"""
Advanced Backtesting Engine
Comprehensive backtesting system with performance analysis and optimization
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json

@dataclass
class Trade:
    """Individual trade record"""
    entry_time: datetime
    exit_time: Optional[datetime] = None
    instrument: str = ""
    direction: str = ""  # BUY or SELL
    entry_price: float = 0.0
    exit_price: Optional[float] = None
    quantity: int = 0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    commission: float = 0.0
    swap: float = 0.0
    profit_loss: Optional[float] = None
    strategy: str = ""
    confidence: float = 0.0
    metadata: Dict = field(default_factory=dict)
    
    def calculate_pnl(self) -> float:
        """Calculate profit/loss for the trade"""
        if self.exit_price is None:
            return 0.0
        
        if self.direction == "BUY":
            pnl = (self.exit_price - self.entry_price) * self.quantity
        else:  # SELL
            pnl = (self.entry_price - self.exit_price) * self.quantity
        
        # Subtract costs
        pnl -= (self.commission + self.swap)
        
        self.profit_loss = pnl
        return pnl
    
    def is_winner(self) -> bool:
        """Check if trade is profitable"""
        return self.calculate_pnl() > 0

@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    # Basic metrics
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    
    # P&L metrics
    total_pnl: float = 0.0
    gross_profit: float = 0.0
    gross_loss: float = 0.0
    profit_factor: float = 0.0
    
    # Risk metrics
    max_drawdown: float = 0.0
    max_drawdown_percent: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # Trade statistics
    avg_win: float = 0.0
    avg_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    avg_trade_duration: timedelta = field(default_factory=lambda: timedelta(0))
    
    # Advanced metrics
    expectancy: float = 0.0
    kelly_criterion: float = 0.0
    var_95: float = 0.0
    cvar_95: float = 0.0
    
    # Time series data
    equity_curve: List[float] = field(default_factory=list)
    drawdown_curve: List[float] = field(default_factory=list)
    trade_returns: List[float] = field(default_factory=list)
    
    # Detailed trade list
    trades: List[Trade] = field(default_factory=list)
    
    # Strategy breakdown
    strategy_performance: Dict[str, Dict] = field(default_factory=dict)

class AdvancedBacktester:
    """
    Advanced backtesting engine with comprehensive analysis
    
    Features:
    - Multi-strategy backtesting
    - Realistic execution simulation
    - Advanced performance metrics
    - Risk analysis
    - Optimization capabilities
    - Detailed reporting
    """
    
    def __init__(self, initial_capital: float = 10000.0, commission: float = 0.0001,
                 spread: float = 0.0001, slippage: float = 0.0001):
        """
        Initialize the backtester
        
        Args:
            initial_capital: Starting capital
            commission: Commission per trade (as decimal)
            spread: Bid-ask spread (as decimal)
            slippage: Execution slippage (as decimal)
        """
        self.initial_capital = initial_capital
        self.commission = commission
        self.spread = spread
        self.slippage = slippage
        
        self.logger = logging.getLogger(__name__)
        
        # Backtest state
        self.current_capital = initial_capital
        self.equity_curve = [initial_capital]
        self.open_trades = {}
        self.closed_trades = []
        self.current_time = None
        
        self.logger.info(f"Backtester initialized with ${initial_capital:,.2f} capital")
    
    def run_backtest(self, market_data: Dict[str, pd.DataFrame], strategies: List[Any],
                    start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> BacktestResults:
        """
        Run comprehensive backtest
        
        Args:
            market_data: Dictionary of market data by instrument
            strategies: List of strategy instances
            start_date: Backtest start date
            end_date: Backtest end date
            
        Returns:
            Comprehensive backtest results
        """
        try:
            self.logger.info("Starting comprehensive backtest...")
            
            # Reset state
            self._reset_state()
            
            # Prepare data
            prepared_data = self._prepare_data(market_data, start_date, end_date)
            
            # Get time index
            time_index = self._get_time_index(prepared_data)
            
            # Run simulation
            for current_time in time_index:
                self.current_time = current_time
                
                # Update market data for current time
                current_market_data = self._get_current_market_data(prepared_data, current_time)
                
                # Process each strategy
                for strategy in strategies:
                    self._process_strategy(strategy, current_market_data, current_time)
                
                # Update open positions
                self._update_open_positions(current_market_data, current_time)
                
                # Update equity curve
                self._update_equity_curve(current_market_data, current_time)
            
            # Close remaining open trades
            self._close_remaining_trades(prepared_data)
            
            # Calculate results
            results = self._calculate_results()
            
            self.logger.info(f"Backtest completed: {results.total_trades} trades, "
                           f"{results.win_rate:.1%} win rate, "
                           f"${results.total_pnl:,.2f} total P&L")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error running backtest: {e}")
            raise
    
    def _reset_state(self):
        """Reset backtester state"""
        self.current_capital = self.initial_capital
        self.equity_curve = [self.initial_capital]
        self.open_trades = {}
        self.closed_trades = []
        self.current_time = None
    
    def _prepare_data(self, market_data: Dict[str, pd.DataFrame], 
                     start_date: Optional[datetime], end_date: Optional[datetime]) -> Dict[str, pd.DataFrame]:
        """Prepare and filter market data"""
        prepared_data = {}
        
        for instrument, data in market_data.items():
            # Ensure datetime index
            if 'timestamp' in data.columns:
                data = data.set_index('timestamp')
            
            # Filter by date range
            if start_date:
                data = data[data.index >= start_date]
            if end_date:
                data = data[data.index <= end_date]
            
            # Sort by time
            data = data.sort_index()
            
            prepared_data[instrument] = data
        
        return prepared_data
    
    def _get_time_index(self, prepared_data: Dict[str, pd.DataFrame]) -> pd.DatetimeIndex:
        """Get unified time index from all instruments"""
        all_times = []
        
        for data in prepared_data.values():
            all_times.extend(data.index.tolist())
        
        # Create unified time index
        time_index = pd.DatetimeIndex(sorted(set(all_times)))
        
        return time_index
    
    def _get_current_market_data(self, prepared_data: Dict[str, pd.DataFrame], 
                                current_time: datetime) -> Dict[str, Dict]:
        """Get market data for current time"""
        current_data = {}
        
        for instrument, data in prepared_data.items():
            # Get data up to current time
            available_data = data[data.index <= current_time]
            
            if len(available_data) > 0:
                latest_row = available_data.iloc[-1]
                
                current_data[instrument] = {
                    'timestamp': current_time,
                    'open': latest_row.get('open', 0),
                    'high': latest_row.get('high', 0),
                    'low': latest_row.get('low', 0),
                    'close': latest_row.get('close', 0),
                    'volume': latest_row.get('volume', 0),
                    'current_price': {
                        'bid': latest_row.get('close', 0) - self.spread/2,
                        'ask': latest_row.get('close', 0) + self.spread/2,
                        'mid': latest_row.get('close', 0),
                        'spread': self.spread
                    },
                    # Include all technical indicators
                    **{col: latest_row.get(col, 0) for col in latest_row.index 
                       if col not in ['open', 'high', 'low', 'close', 'volume']}
                }
        
        return current_data
    
    def _process_strategy(self, strategy: Any, market_data: Dict[str, Dict], current_time: datetime):
        """Process strategy signals and execute trades"""
        try:
            # Get strategy signals for each instrument
            for instrument, data in market_data.items():
                # Prepare data for strategy
                strategy_data = {
                    'instrument': instrument,
                    'current_price': data['current_price'],
                    'timestamp': current_time
                }
                
                # Add timeframe data (simplified - using current data for all timeframes)
                for tf in getattr(strategy, 'timeframes', ['H1']):
                    strategy_data[tf] = pd.DataFrame([data])
                
                # Get signal from strategy
                signal = strategy.analyze(strategy_data)
                
                # Execute signal if valid
                if signal and signal.signal_type in ['BUY', 'SELL']:
                    if strategy.should_trade(signal, current_time):
                        self._execute_trade(signal, strategy.name, current_time)
                        
        except Exception as e:
            self.logger.error(f"Error processing strategy {strategy.name}: {e}")
    
    def _execute_trade(self, signal: Any, strategy_name: str, current_time: datetime):
        """Execute a trade based on signal"""
        try:
            instrument = signal.instrument
            direction = signal.signal_type
            entry_price = signal.entry_price
            
            # Apply slippage
            if direction == 'BUY':
                execution_price = entry_price + self.slippage
            else:
                execution_price = entry_price - self.slippage
            
            # Calculate position size (simplified)
            risk_amount = self.current_capital * 0.02  # 2% risk
            if signal.stop_loss:
                stop_distance = abs(execution_price - signal.stop_loss)
                quantity = int(risk_amount / stop_distance) if stop_distance > 0 else 1000
            else:
                quantity = 1000  # Default quantity
            
            # Create trade
            trade = Trade(
                entry_time=current_time,
                instrument=instrument,
                direction=direction,
                entry_price=execution_price,
                quantity=quantity,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                commission=abs(quantity * execution_price * self.commission),
                strategy=strategy_name,
                confidence=signal.confidence
            )
            
            # Add to open trades
            trade_id = f"{instrument}_{current_time.timestamp()}"
            self.open_trades[trade_id] = trade
            
            self.logger.debug(f"Executed {direction} trade: {instrument} @ {execution_price:.5f}")
            
        except Exception as e:
            self.logger.error(f"Error executing trade: {e}")
    
    def _update_open_positions(self, market_data: Dict[str, Dict], current_time: datetime):
        """Update open positions and check for exits"""
        trades_to_close = []
        
        for trade_id, trade in self.open_trades.items():
            instrument = trade.instrument
            
            if instrument not in market_data:
                continue
            
            current_price = market_data[instrument]['current_price']['mid']
            
            # Check stop loss
            if trade.stop_loss:
                if ((trade.direction == 'BUY' and current_price <= trade.stop_loss) or
                    (trade.direction == 'SELL' and current_price >= trade.stop_loss)):
                    trades_to_close.append((trade_id, current_price, 'stop_loss'))
                    continue
            
            # Check take profit
            if trade.take_profit:
                if ((trade.direction == 'BUY' and current_price >= trade.take_profit) or
                    (trade.direction == 'SELL' and current_price <= trade.take_profit)):
                    trades_to_close.append((trade_id, current_price, 'take_profit'))
                    continue
        
        # Close trades
        for trade_id, exit_price, exit_reason in trades_to_close:
            self._close_trade(trade_id, exit_price, current_time, exit_reason)
    
    def _close_trade(self, trade_id: str, exit_price: float, exit_time: datetime, reason: str = ""):
        """Close a trade"""
        if trade_id not in self.open_trades:
            return
        
        trade = self.open_trades[trade_id]
        
        # Apply slippage
        if trade.direction == 'BUY':
            execution_price = exit_price - self.slippage
        else:
            execution_price = exit_price + self.slippage
        
        # Update trade
        trade.exit_time = exit_time
        trade.exit_price = execution_price
        trade.commission += abs(trade.quantity * execution_price * self.commission)
        
        # Calculate P&L
        pnl = trade.calculate_pnl()
        
        # Update capital
        self.current_capital += pnl
        
        # Move to closed trades
        self.closed_trades.append(trade)
        del self.open_trades[trade_id]
        
        self.logger.debug(f"Closed trade: {trade.instrument} P&L: ${pnl:.2f} ({reason})")
    
    def _update_equity_curve(self, market_data: Dict[str, Dict], current_time: datetime):
        """Update equity curve with current unrealized P&L"""
        unrealized_pnl = 0
        
        for trade in self.open_trades.values():
            if trade.instrument in market_data:
                current_price = market_data[trade.instrument]['current_price']['mid']
                
                if trade.direction == 'BUY':
                    unrealized = (current_price - trade.entry_price) * trade.quantity
                else:
                    unrealized = (trade.entry_price - current_price) * trade.quantity
                
                unrealized_pnl += unrealized
        
        current_equity = self.current_capital + unrealized_pnl
        self.equity_curve.append(current_equity)
    
    def _close_remaining_trades(self, prepared_data: Dict[str, pd.DataFrame]):
        """Close any remaining open trades at the end"""
        for trade_id, trade in list(self.open_trades.items()):
            instrument = trade.instrument
            
            if instrument in prepared_data:
                final_price = prepared_data[instrument]['close'].iloc[-1]
                self._close_trade(trade_id, final_price, self.current_time, "end_of_backtest")
    
    def _calculate_results(self) -> BacktestResults:
        """Calculate comprehensive backtest results"""
        results = BacktestResults()
        
        if not self.closed_trades:
            return results
        
        # Basic trade statistics
        results.trades = self.closed_trades
        results.total_trades = len(self.closed_trades)
        results.winning_trades = sum(1 for trade in self.closed_trades if trade.is_winner())
        results.losing_trades = results.total_trades - results.winning_trades
        results.win_rate = results.winning_trades / results.total_trades if results.total_trades > 0 else 0
        
        # P&L calculations
        trade_pnls = [trade.calculate_pnl() for trade in self.closed_trades]
        results.total_pnl = sum(trade_pnls)
        results.gross_profit = sum(pnl for pnl in trade_pnls if pnl > 0)
        results.gross_loss = abs(sum(pnl for pnl in trade_pnls if pnl < 0))
        results.profit_factor = results.gross_profit / results.gross_loss if results.gross_loss > 0 else float('inf')
        
        # Trade statistics
        winning_trades = [pnl for pnl in trade_pnls if pnl > 0]
        losing_trades = [pnl for pnl in trade_pnls if pnl < 0]
        
        results.avg_win = np.mean(winning_trades) if winning_trades else 0
        results.avg_loss = np.mean(losing_trades) if losing_trades else 0
        results.largest_win = max(winning_trades) if winning_trades else 0
        results.largest_loss = min(losing_trades) if losing_trades else 0
        
        # Trade duration
        durations = [(trade.exit_time - trade.entry_time) for trade in self.closed_trades if trade.exit_time]
        results.avg_trade_duration = np.mean(durations) if durations else timedelta(0)
        
        # Risk metrics
        results.equity_curve = self.equity_curve
        results.trade_returns = [pnl / self.initial_capital for pnl in trade_pnls]
        
        # Calculate drawdown
        peak = self.initial_capital
        max_dd = 0
        max_dd_pct = 0
        drawdown_curve = []
        
        for equity in self.equity_curve:
            if equity > peak:
                peak = equity
            
            drawdown = peak - equity
            drawdown_pct = drawdown / peak if peak > 0 else 0
            
            drawdown_curve.append(drawdown)
            
            if drawdown > max_dd:
                max_dd = drawdown
            if drawdown_pct > max_dd_pct:
                max_dd_pct = drawdown_pct
        
        results.max_drawdown = max_dd
        results.max_drawdown_percent = max_dd_pct
        results.drawdown_curve = drawdown_curve
        
        # Advanced metrics
        if results.trade_returns:
            returns_array = np.array(results.trade_returns)
            
            # Sharpe ratio (assuming risk-free rate = 0)
            if np.std(returns_array) > 0:
                results.sharpe_ratio = np.mean(returns_array) / np.std(returns_array) * np.sqrt(252)
            
            # Sortino ratio
            negative_returns = returns_array[returns_array < 0]
            if len(negative_returns) > 0 and np.std(negative_returns) > 0:
                results.sortino_ratio = np.mean(returns_array) / np.std(negative_returns) * np.sqrt(252)
            
            # Calmar ratio
            if results.max_drawdown_percent > 0:
                annual_return = (self.equity_curve[-1] / self.initial_capital) ** (252 / len(self.equity_curve)) - 1
                results.calmar_ratio = annual_return / results.max_drawdown_percent
            
            # Expectancy
            results.expectancy = results.win_rate * results.avg_win - (1 - results.win_rate) * abs(results.avg_loss)
            
            # Kelly Criterion
            if results.avg_loss != 0:
                win_loss_ratio = results.avg_win / abs(results.avg_loss)
                results.kelly_criterion = results.win_rate - (1 - results.win_rate) / win_loss_ratio
            
            # VaR and CVaR
            results.var_95 = np.percentile(returns_array, 5)
            tail_returns = returns_array[returns_array <= results.var_95]
            results.cvar_95 = np.mean(tail_returns) if len(tail_returns) > 0 else 0
        
        # Strategy breakdown
        strategy_stats = {}
        for trade in self.closed_trades:
            strategy = trade.strategy
            if strategy not in strategy_stats:
                strategy_stats[strategy] = {'trades': [], 'pnl': 0}
            
            strategy_stats[strategy]['trades'].append(trade)
            strategy_stats[strategy]['pnl'] += trade.calculate_pnl()
        
        for strategy, stats in strategy_stats.items():
            trades = stats['trades']
            winning = sum(1 for t in trades if t.is_winner())
            
            results.strategy_performance[strategy] = {
                'total_trades': len(trades),
                'winning_trades': winning,
                'win_rate': winning / len(trades) if trades else 0,
                'total_pnl': stats['pnl'],
                'avg_pnl': stats['pnl'] / len(trades) if trades else 0
            }
        
        return results

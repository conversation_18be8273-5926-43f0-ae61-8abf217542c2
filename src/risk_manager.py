"""
Risk Manager Module
Handles position sizing, risk limits, and capital protection
"""

import os
import logging
from typing import Dict, Optional, Tuple
from datetime import datetime, timedelta
import json

class RiskManager:
    """Manages trading risk and position sizing"""
    
    def __init__(self):
        """Initialize the risk manager with configuration"""
        self.logger = logging.getLogger(__name__)
        
        # Load risk parameters from environment
        self.max_risk_per_trade = float(os.getenv('MAX_RISK_PER_TRADE', 2.0)) / 100  # Convert to decimal
        self.max_daily_loss = float(os.getenv('MAX_DAILY_LOSS', 100))
        self.max_open_positions = int(os.getenv('MAX_OPEN_POSITIONS', 3))
        self.default_trade_amount = float(os.getenv('DEFAULT_TRADE_AMOUNT', 1000))
        self.stop_loss_pips = float(os.getenv('STOP_LOSS_PIPS', 20))
        self.take_profit_pips = float(os.getenv('TAKE_PROFIT_PIPS', 40))
        
        # Track daily statistics
        self.daily_stats = {
            'date': datetime.now().date(),
            'trades_count': 0,
            'realized_pl': 0.0,
            'max_drawdown': 0.0,
            'starting_balance': 0.0
        }
        
        self.logger.info(f"Risk Manager initialized - Max risk per trade: {self.max_risk_per_trade*100}%")
    
    def calculate_position_size(self, account_balance: float, entry_price: float, 
                              stop_loss: Optional[float], instrument: str, 
                              ai_confidence: float = 0.7) -> Tuple[int, Dict]:
        """
        Calculate optimal position size based on risk parameters
        
        Args:
            account_balance: Current account balance
            entry_price: Planned entry price
            stop_loss: Stop loss price level
            instrument: Currency pair
            ai_confidence: AI confidence level (0.0 to 1.0)
            
        Returns:
            Tuple of (position_size, risk_info)
        """
        try:
            # Get pip value for the instrument
            pip_value = self._get_pip_value(instrument, account_balance)
            
            # Calculate risk amount
            max_risk_amount = account_balance * self.max_risk_per_trade
            
            # Adjust risk based on AI confidence
            confidence_multiplier = min(ai_confidence * 1.5, 1.0)  # Max 1.0
            adjusted_risk_amount = max_risk_amount * confidence_multiplier
            
            if stop_loss is None:
                # Use default stop loss in pips
                if 'JPY' in instrument:
                    stop_loss_distance = self.stop_loss_pips * 0.01  # For JPY pairs
                else:
                    stop_loss_distance = self.stop_loss_pips * 0.0001  # For other pairs
                
                # Determine stop loss based on trade direction
                # This is a simplified approach - in practice, you'd know the direction
                stop_loss_distance = abs(stop_loss_distance)
            else:
                stop_loss_distance = abs(entry_price - stop_loss)
            
            # Calculate position size
            if stop_loss_distance > 0:
                # Position size = Risk Amount / (Stop Loss Distance * Pip Value * Units per Pip)
                risk_per_pip = stop_loss_distance / self._get_pip_size(instrument)
                position_size = int(adjusted_risk_amount / (risk_per_pip * pip_value))
            else:
                # Fallback to default amount
                position_size = int(self.default_trade_amount)
            
            # Apply position size limits
            min_position = 1
            max_position = int(account_balance * 0.1)  # Max 10% of balance as position size
            position_size = max(min_position, min(position_size, max_position))
            
            # Calculate actual risk
            actual_risk = (stop_loss_distance / self._get_pip_size(instrument)) * pip_value * position_size
            risk_percentage = (actual_risk / account_balance) * 100
            
            risk_info = {
                'position_size': position_size,
                'risk_amount': actual_risk,
                'risk_percentage': risk_percentage,
                'stop_loss_distance': stop_loss_distance,
                'pip_value': pip_value,
                'confidence_multiplier': confidence_multiplier,
                'max_risk_amount': max_risk_amount
            }
            
            self.logger.info(f"Position size calculated: {position_size} units, Risk: {risk_percentage:.2f}%")
            return position_size, risk_info
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            # Return conservative default
            return int(self.default_trade_amount), {
                'position_size': int(self.default_trade_amount),
                'risk_amount': 0,
                'risk_percentage': 0,
                'error': str(e)
            }
    
    def check_trading_allowed(self, account_summary: Dict, current_positions: list) -> Tuple[bool, str]:
        """
        Check if trading is allowed based on risk limits
        
        Args:
            account_summary: Account summary from trade executor
            current_positions: List of current open positions
            
        Returns:
            Tuple of (allowed, reason)
        """
        try:
            # Check if it's a new trading day
            current_date = datetime.now().date()
            if self.daily_stats['date'] != current_date:
                self._reset_daily_stats(account_summary.get('balance', 0))
            
            # Check maximum open positions
            if len(current_positions) >= self.max_open_positions:
                return False, f"Maximum open positions reached ({self.max_open_positions})"
            
            # Check daily loss limit
            current_daily_pl = account_summary.get('realized_pl', 0) - self.daily_stats['starting_balance']
            if current_daily_pl <= -self.max_daily_loss:
                return False, f"Daily loss limit reached ({self.max_daily_loss})"
            
            # Check account balance
            balance = account_summary.get('balance', 0)
            if balance <= 0:
                return False, "Insufficient account balance"
            
            # Check margin availability
            margin_available = account_summary.get('margin_available', 0)
            if margin_available <= 100:  # Minimum margin requirement
                return False, "Insufficient margin available"
            
            # Check unrealized P&L
            unrealized_pl = account_summary.get('unrealized_pl', 0)
            if unrealized_pl <= -self.max_daily_loss:
                return False, "Unrealized loss limit reached"
            
            return True, "Trading allowed"
            
        except Exception as e:
            self.logger.error(f"Error checking trading allowance: {e}")
            return False, f"Risk check error: {str(e)}"
    
    def validate_trade_signal(self, signal: Dict, account_summary: Dict, 
                            current_positions: list) -> Tuple[bool, Dict]:
        """
        Validate a trade signal against risk parameters
        
        Args:
            signal: AI trading signal
            account_summary: Account summary
            current_positions: Current open positions
            
        Returns:
            Tuple of (valid, validated_signal)
        """
        try:
            # Check if trading is allowed
            trading_allowed, reason = self.check_trading_allowed(account_summary, current_positions)
            if not trading_allowed:
                return False, {'error': reason}
            
            # Skip validation for HOLD signals
            if signal['signal'] == 'HOLD':
                return True, signal
            
            # Check confidence level
            if signal['confidence'] < 0.6:
                return False, {'error': 'AI confidence too low for trading'}
            
            # Calculate position size
            balance = account_summary.get('balance', 0)
            position_size, risk_info = self.calculate_position_size(
                balance,
                signal['entry_price'],
                signal.get('stop_loss'),
                signal.get('instrument', 'EUR_USD'),
                signal['confidence']
            )
            
            # Validate risk percentage
            if risk_info['risk_percentage'] > self.max_risk_per_trade * 100:
                return False, {'error': f"Risk too high: {risk_info['risk_percentage']:.2f}%"}
            
            # Create validated signal
            validated_signal = signal.copy()
            validated_signal['position_size'] = position_size
            validated_signal['risk_info'] = risk_info
            
            # Ensure stop loss and take profit are set
            if not validated_signal.get('stop_loss'):
                validated_signal['stop_loss'] = self._calculate_default_stop_loss(
                    signal['entry_price'], 
                    signal['signal'], 
                    signal.get('instrument', 'EUR_USD')
                )
            
            if not validated_signal.get('take_profit'):
                validated_signal['take_profit'] = self._calculate_default_take_profit(
                    signal['entry_price'], 
                    signal['signal'], 
                    signal.get('instrument', 'EUR_USD')
                )
            
            return True, validated_signal
            
        except Exception as e:
            self.logger.error(f"Error validating trade signal: {e}")
            return False, {'error': str(e)}
    
    def update_daily_stats(self, trade_result: Dict):
        """Update daily trading statistics"""
        try:
            current_date = datetime.now().date()
            if self.daily_stats['date'] != current_date:
                self._reset_daily_stats(0)  # Will be updated with actual balance
            
            if trade_result.get('success'):
                self.daily_stats['trades_count'] += 1
                pl = trade_result.get('pl', 0)
                self.daily_stats['realized_pl'] += pl
                
                # Update max drawdown if this is a loss
                if pl < 0:
                    current_drawdown = abs(self.daily_stats['realized_pl'])
                    self.daily_stats['max_drawdown'] = max(
                        self.daily_stats['max_drawdown'], 
                        current_drawdown
                    )
            
            self.logger.debug(f"Daily stats updated: {self.daily_stats}")
            
        except Exception as e:
            self.logger.error(f"Error updating daily stats: {e}")
    
    def get_risk_summary(self, account_summary: Dict) -> Dict:
        """Get current risk summary"""
        try:
            balance = account_summary.get('balance', 0)
            unrealized_pl = account_summary.get('unrealized_pl', 0)
            
            # Calculate daily P&L
            daily_pl = self.daily_stats['realized_pl']
            if self.daily_stats['starting_balance'] > 0:
                daily_pl_pct = (daily_pl / self.daily_stats['starting_balance']) * 100
            else:
                daily_pl_pct = 0
            
            # Calculate account utilization
            margin_used = account_summary.get('margin_used', 0)
            margin_available = account_summary.get('margin_available', 0)
            total_margin = margin_used + margin_available
            utilization_pct = (margin_used / total_margin * 100) if total_margin > 0 else 0
            
            risk_summary = {
                'account_balance': balance,
                'unrealized_pl': unrealized_pl,
                'daily_trades': self.daily_stats['trades_count'],
                'daily_pl': daily_pl,
                'daily_pl_percentage': daily_pl_pct,
                'max_drawdown': self.daily_stats['max_drawdown'],
                'margin_utilization': utilization_pct,
                'max_risk_per_trade': self.max_risk_per_trade * 100,
                'max_daily_loss': self.max_daily_loss,
                'max_open_positions': self.max_open_positions,
                'open_positions': account_summary.get('open_position_count', 0)
            }
            
            return risk_summary
            
        except Exception as e:
            self.logger.error(f"Error getting risk summary: {e}")
            return {}
    
    def _reset_daily_stats(self, starting_balance: float):
        """Reset daily statistics for a new trading day"""
        self.daily_stats = {
            'date': datetime.now().date(),
            'trades_count': 0,
            'realized_pl': 0.0,
            'max_drawdown': 0.0,
            'starting_balance': starting_balance
        }
        self.logger.info("Daily statistics reset for new trading day")
    
    def _get_pip_value(self, instrument: str, account_balance: float) -> float:
        """Get pip value for position sizing calculations"""
        # Simplified pip value calculation
        # In practice, this should be more sophisticated
        if 'JPY' in instrument:
            return 0.01  # For JPY pairs
        else:
            return 0.0001  # For other major pairs
    
    def _get_pip_size(self, instrument: str) -> float:
        """Get pip size for the instrument"""
        if 'JPY' in instrument:
            return 0.01
        else:
            return 0.0001
    
    def _calculate_default_stop_loss(self, entry_price: float, signal: str, instrument: str) -> float:
        """Calculate default stop loss based on pips"""
        pip_size = self._get_pip_size(instrument)
        stop_distance = self.stop_loss_pips * pip_size
        
        if signal == 'BUY':
            return entry_price - stop_distance
        else:  # SELL
            return entry_price + stop_distance
    
    def _calculate_default_take_profit(self, entry_price: float, signal: str, instrument: str) -> float:
        """Calculate default take profit based on pips"""
        pip_size = self._get_pip_size(instrument)
        profit_distance = self.take_profit_pips * pip_size
        
        if signal == 'BUY':
            return entry_price + profit_distance
        else:  # SELL
            return entry_price - profit_distance

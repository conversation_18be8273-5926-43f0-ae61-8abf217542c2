"""
Data Fetcher <PERSON><PERSON><PERSON>
Handles fetching real-time and historical forex data from OANDA
"""

import os
import logging
import pandas as pd
from datetime import datetime, timedelta
import oandapyV20
import oandapyV20.endpoints.pricing as pricing
import oandapyV20.endpoints.instruments as instruments
from typing import Dict, List, Optional

class DataFetcher:
    """Fetches forex market data from OANDA API"""
    
    def __init__(self):
        """Initialize the data fetcher with OANDA credentials"""
        self.logger = logging.getLogger(__name__)
        
        # Get OANDA credentials from environment
        self.api_key = os.getenv('OANDA_API_KEY')
        self.account_id = os.getenv('OANDA_ACCOUNT_ID')
        self.environment = os.getenv('OANDA_ENVIRONMENT', 'practice')
        
        if not self.api_key or not self.account_id:
            raise ValueError("OANDA API credentials not found in environment variables")
        
        # Initialize OANDA client
        self.client = oandapyV20.API(
            access_token=self.api_key,
            environment=self.environment
        )
        
        self.logger.info(f"DataFetcher initialized for {self.environment} environment")
    
    def get_current_price(self, instrument: str) -> Dict:
        """
        Get current bid/ask prices for an instrument
        
        Args:
            instrument: Currency pair (e.g., 'EUR_USD')
            
        Returns:
            Dict with current price information
        """
        try:
            request = pricing.PricingInfo(
                accountID=self.account_id,
                params={"instruments": instrument}
            )
            
            response = self.client.request(request)
            
            if response['prices']:
                price_data = response['prices'][0]
                
                current_price = {
                    'instrument': instrument,
                    'time': price_data['time'],
                    'bid': float(price_data['bids'][0]['price']),
                    'ask': float(price_data['asks'][0]['price']),
                    'spread': float(price_data['asks'][0]['price']) - float(price_data['bids'][0]['price']),
                    'mid': (float(price_data['bids'][0]['price']) + float(price_data['asks'][0]['price'])) / 2
                }
                
                self.logger.debug(f"Current price for {instrument}: {current_price['mid']:.5f}")
                return current_price
            else:
                raise ValueError(f"No price data available for {instrument}")
                
        except Exception as e:
            self.logger.error(f"Error fetching current price for {instrument}: {e}")
            raise
    
    def get_historical_data(self, instrument: str, granularity: str = 'H1', count: int = 100) -> pd.DataFrame:
        """
        Get historical candlestick data
        
        Args:
            instrument: Currency pair (e.g., 'EUR_USD')
            granularity: Time granularity ('M1', 'M5', 'H1', 'D', etc.)
            count: Number of candles to fetch
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            request = instruments.InstrumentsCandles(
                instrument=instrument,
                params={
                    'granularity': granularity,
                    'count': count
                }
            )
            
            response = self.client.request(request)
            
            # Convert to DataFrame
            candles_data = []
            for candle in response['candles']:
                if candle['complete']:  # Only use complete candles
                    candles_data.append({
                        'time': pd.to_datetime(candle['time']),
                        'open': float(candle['mid']['o']),
                        'high': float(candle['mid']['h']),
                        'low': float(candle['mid']['l']),
                        'close': float(candle['mid']['c']),
                        'volume': int(candle['volume'])
                    })
            
            df = pd.DataFrame(candles_data)
            df.set_index('time', inplace=True)
            df.sort_index(inplace=True)
            
            self.logger.info(f"Fetched {len(df)} candles for {instrument} ({granularity})")
            return df
            
        except Exception as e:
            self.logger.error(f"Error fetching historical data for {instrument}: {e}")
            raise
    
    def get_multiple_prices(self, instruments: List[str]) -> Dict:
        """
        Get current prices for multiple instruments
        
        Args:
            instruments: List of currency pairs
            
        Returns:
            Dict with prices for each instrument
        """
        try:
            instruments_str = ','.join(instruments)
            
            request = pricing.PricingInfo(
                accountID=self.account_id,
                params={"instruments": instruments_str}
            )
            
            response = self.client.request(request)
            
            prices = {}
            for price_data in response['prices']:
                instrument = price_data['instrument']
                prices[instrument] = {
                    'time': price_data['time'],
                    'bid': float(price_data['bids'][0]['price']),
                    'ask': float(price_data['asks'][0]['price']),
                    'spread': float(price_data['asks'][0]['price']) - float(price_data['bids'][0]['price']),
                    'mid': (float(price_data['bids'][0]['price']) + float(price_data['asks'][0]['price'])) / 2
                }
            
            self.logger.debug(f"Fetched prices for {len(prices)} instruments")
            return prices
            
        except Exception as e:
            self.logger.error(f"Error fetching multiple prices: {e}")
            raise
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate basic technical indicators
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with added technical indicators
        """
        try:
            # Simple Moving Averages
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['sma_50'] = df['close'].rolling(window=50).mean()
            
            # Exponential Moving Averages
            df['ema_12'] = df['close'].ewm(span=12).mean()
            df['ema_26'] = df['close'].ewm(span=26).mean()
            
            # MACD
            df['macd'] = df['ema_12'] - df['ema_26']
            df['macd_signal'] = df['macd'].ewm(span=9).mean()
            df['macd_histogram'] = df['macd'] - df['macd_signal']
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=20).mean()
            bb_std = df['close'].rolling(window=20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            
            # Price position within Bollinger Bands
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            self.logger.debug("Technical indicators calculated")
            return df
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            raise
    
    def get_market_analysis_data(self, instrument: str) -> Dict:
        """
        Get comprehensive market data for analysis
        
        Args:
            instrument: Currency pair to analyze
            
        Returns:
            Dict with current price and technical analysis
        """
        try:
            # Get current price
            current_price = self.get_current_price(instrument)
            
            # Get historical data for different timeframes
            h1_data = self.get_historical_data(instrument, 'H1', 100)
            h4_data = self.get_historical_data(instrument, 'H4', 50)
            d1_data = self.get_historical_data(instrument, 'D', 30)
            
            # Calculate technical indicators
            h1_data = self.calculate_technical_indicators(h1_data)
            h4_data = self.calculate_technical_indicators(h4_data)
            d1_data = self.calculate_technical_indicators(d1_data)
            
            # Get latest values
            latest_h1 = h1_data.iloc[-1]
            latest_h4 = h4_data.iloc[-1]
            latest_d1 = d1_data.iloc[-1]
            
            analysis_data = {
                'instrument': instrument,
                'current_price': current_price,
                'h1_analysis': {
                    'close': latest_h1['close'],
                    'sma_20': latest_h1['sma_20'],
                    'sma_50': latest_h1['sma_50'],
                    'rsi': latest_h1['rsi'],
                    'macd': latest_h1['macd'],
                    'macd_signal': latest_h1['macd_signal'],
                    'bb_position': latest_h1['bb_position']
                },
                'h4_analysis': {
                    'close': latest_h4['close'],
                    'sma_20': latest_h4['sma_20'],
                    'sma_50': latest_h4['sma_50'],
                    'rsi': latest_h4['rsi'],
                    'macd': latest_h4['macd'],
                    'bb_position': latest_h4['bb_position']
                },
                'daily_analysis': {
                    'close': latest_d1['close'],
                    'sma_20': latest_d1['sma_20'],
                    'sma_50': latest_d1['sma_50'],
                    'rsi': latest_d1['rsi'],
                    'bb_position': latest_d1['bb_position']
                }
            }
            
            self.logger.info(f"Market analysis data prepared for {instrument}")
            return analysis_data
            
        except Exception as e:
            self.logger.error(f"Error getting market analysis data: {e}")
            raise

"""
Advanced Market Data Provider
Comprehensive market data fetching with multiple sources and advanced indicators
"""

import pandas as pd
import numpy as np
import requests
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import yfinance as yf
from dataclasses import dataclass

@dataclass
class MarketDataConfig:
    """Configuration for market data provider"""
    primary_source: str = "yahoo"  # yahoo, alpha_vantage, fxcm
    backup_sources: List[str] = None
    cache_duration: int = 300  # 5 minutes
    max_retries: int = 3
    timeout: int = 30
    
    def __post_init__(self):
        if self.backup_sources is None:
            self.backup_sources = ["yahoo"]

class AdvancedDataProvider:
    """
    Advanced market data provider with multiple sources and comprehensive indicators
    
    Features:
    - Multiple data sources with fallback
    - Real-time and historical data
    - Advanced technical indicators
    - Economic calendar integration
    - Data caching and validation
    """
    
    def __init__(self, config: MarketDataConfig = None):
        """Initialize the advanced data provider"""
        self.config = config or MarketDataConfig()
        self.logger = logging.getLogger(__name__)
        self.cache = {}
        self.last_update = {}
        
        # Forex symbol mapping
        self.forex_symbols = {
            'EUR_USD': 'EURUSD=X',
            'GBP_USD': 'GBPUSD=X',
            'USD_JPY': 'USDJPY=X',
            'AUD_USD': 'AUDUSD=X',
            'USD_CAD': 'USDCAD=X',
            'USD_CHF': 'USDCHF=X',
            'NZD_USD': 'NZDUSD=X',
            'EUR_GBP': 'EURGBP=X',
            'EUR_JPY': 'EURJPY=X',
            'GBP_JPY': 'GBPJPY=X'
        }
        
        self.logger.info(f"Advanced Data Provider initialized with primary source: {self.config.primary_source}")
    
    def get_multi_timeframe_data(self, instrument: str, timeframes: List[str], 
                                periods: Dict[str, int] = None) -> Dict[str, pd.DataFrame]:
        """
        Get data for multiple timeframes
        
        Args:
            instrument: Currency pair (e.g., 'EUR_USD')
            timeframes: List of timeframes ['M1', 'M5', 'M15', 'H1', 'H4', 'D1']
            periods: Number of periods for each timeframe
            
        Returns:
            Dictionary with timeframe data
        """
        try:
            if periods is None:
                periods = {
                    'M1': 500,
                    'M5': 500,
                    'M15': 500,
                    'H1': 500,
                    'H4': 200,
                    'D1': 100
                }
            
            multi_data = {}
            
            for timeframe in timeframes:
                try:
                    period_count = periods.get(timeframe, 100)
                    data = self._get_historical_data(instrument, timeframe, period_count)
                    
                    if data is not None and len(data) > 0:
                        # Calculate all technical indicators
                        data = self._calculate_all_indicators(data)
                        multi_data[timeframe] = data
                        self.logger.debug(f"Retrieved {len(data)} periods for {instrument} {timeframe}")
                    else:
                        self.logger.warning(f"No data retrieved for {instrument} {timeframe}")
                        
                except Exception as e:
                    self.logger.error(f"Error getting data for {instrument} {timeframe}: {e}")
                    continue
            
            return multi_data
            
        except Exception as e:
            self.logger.error(f"Error getting multi-timeframe data: {e}")
            return {}
    
    def get_real_time_data(self, instruments: List[str]) -> Dict[str, Dict]:
        """
        Get real-time market data for multiple instruments
        
        Args:
            instruments: List of currency pairs
            
        Returns:
            Dictionary with real-time data
        """
        try:
            real_time_data = {}
            
            for instrument in instruments:
                try:
                    # Check cache first
                    cache_key = f"realtime_{instrument}"
                    if self._is_cache_valid(cache_key):
                        real_time_data[instrument] = self.cache[cache_key]
                        continue
                    
                    # Get current price data
                    symbol = self._get_yahoo_symbol(instrument)
                    ticker = yf.Ticker(symbol)
                    
                    # Get current price
                    info = ticker.info
                    current_price = info.get('regularMarketPrice', 0)
                    
                    if current_price == 0:
                        # Try alternative method
                        hist = ticker.history(period="1d", interval="1m")
                        if len(hist) > 0:
                            current_price = hist['Close'].iloc[-1]
                    
                    # Calculate bid/ask spread (estimated)
                    spread = current_price * 0.0001  # Rough estimate
                    
                    price_data = {
                        'timestamp': datetime.now(),
                        'bid': current_price - spread/2,
                        'ask': current_price + spread/2,
                        'mid': current_price,
                        'spread': spread,
                        'volume': info.get('volume', 0)
                    }
                    
                    # Cache the data
                    self.cache[cache_key] = price_data
                    self.last_update[cache_key] = datetime.now()
                    
                    real_time_data[instrument] = price_data
                    
                except Exception as e:
                    self.logger.error(f"Error getting real-time data for {instrument}: {e}")
                    continue
            
            return real_time_data
            
        except Exception as e:
            self.logger.error(f"Error getting real-time data: {e}")
            return {}
    
    def _get_historical_data(self, instrument: str, timeframe: str, periods: int) -> pd.DataFrame:
        """Get historical data for a specific timeframe"""
        try:
            # Convert timeframe to Yahoo Finance format
            interval_map = {
                'M1': '1m',
                'M5': '5m',
                'M15': '15m',
                'M30': '30m',
                'H1': '1h',
                'H4': '4h',
                'D1': '1d',
                'W1': '1wk',
                'MN1': '1mo'
            }
            
            interval = interval_map.get(timeframe, '1h')
            symbol = self._get_yahoo_symbol(instrument)
            
            # Calculate period for Yahoo Finance
            if timeframe in ['M1', 'M5', 'M15', 'M30']:
                period = "7d"  # Last 7 days for minute data
            elif timeframe in ['H1', 'H4']:
                period = "60d"  # Last 60 days for hourly data
            else:
                period = "2y"  # Last 2 years for daily+ data
            
            # Get data from Yahoo Finance
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if len(data) == 0:
                self.logger.warning(f"No historical data for {symbol} {interval}")
                return pd.DataFrame()
            
            # Rename columns to standard format
            data = data.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })
            
            # Take only the requested number of periods
            if len(data) > periods:
                data = data.tail(periods)
            
            # Reset index to make datetime a column
            data.reset_index(inplace=True)
            data.rename(columns={'Datetime': 'timestamp'}, inplace=True)
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error getting historical data: {e}")
            return pd.DataFrame()
    
    def _get_yahoo_symbol(self, instrument: str) -> str:
        """Convert instrument to Yahoo Finance symbol"""
        return self.forex_symbols.get(instrument, f"{instrument}=X")
    
    def _calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators"""
        try:
            if len(data) < 50:
                return data
            
            # Moving Averages
            data['sma_10'] = data['close'].rolling(window=10).mean()
            data['sma_20'] = data['close'].rolling(window=20).mean()
            data['sma_50'] = data['close'].rolling(window=50).mean()
            data['sma_100'] = data['close'].rolling(window=100).mean()
            data['sma_200'] = data['close'].rolling(window=200).mean()
            
            # Exponential Moving Averages
            data['ema_8'] = data['close'].ewm(span=8).mean()
            data['ema_12'] = data['close'].ewm(span=12).mean()
            data['ema_21'] = data['close'].ewm(span=21).mean()
            data['ema_26'] = data['close'].ewm(span=26).mean()
            data['ema_50'] = data['close'].ewm(span=50).mean()
            data['ema_100'] = data['close'].ewm(span=100).mean()
            
            # For strategy compatibility
            data['ema_fast'] = data['ema_12']
            data['ema_slow'] = data['ema_26']
            
            # MACD
            data['macd'] = data['ema_12'] - data['ema_26']
            data['macd_signal'] = data['macd'].ewm(span=9).mean()
            data['macd_histogram'] = data['macd'] - data['macd_signal']
            
            # RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['rsi'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            data['bb_middle'] = data['close'].rolling(window=20).mean()
            bb_std = data['close'].rolling(window=20).std()
            data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
            data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
            data['bb_width'] = data['bb_upper'] - data['bb_lower']
            data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
            
            # ATR (Average True Range)
            data['tr1'] = data['high'] - data['low']
            data['tr2'] = abs(data['high'] - data['close'].shift())
            data['tr3'] = abs(data['low'] - data['close'].shift())
            data['true_range'] = data[['tr1', 'tr2', 'tr3']].max(axis=1)
            data['atr'] = data['true_range'].rolling(window=14).mean()
            
            # Stochastic Oscillator
            data['lowest_low'] = data['low'].rolling(window=14).min()
            data['highest_high'] = data['high'].rolling(window=14).max()
            data['stoch_k'] = 100 * (data['close'] - data['lowest_low']) / (data['highest_high'] - data['lowest_low'])
            data['stoch_d'] = data['stoch_k'].rolling(window=3).mean()
            
            # ADX and Directional Movement
            data = self._calculate_adx(data)
            
            # VWAP (Volume Weighted Average Price)
            data['vwap'] = (data['close'] * data['volume']).cumsum() / data['volume'].cumsum()
            
            # Parabolic SAR
            data = self._calculate_parabolic_sar(data)
            
            # Clean up temporary columns
            temp_cols = ['tr1', 'tr2', 'tr3', 'true_range', 'lowest_low', 'highest_high']
            data.drop(columns=[col for col in temp_cols if col in data.columns], inplace=True)
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return data
    
    def _calculate_adx(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate ADX and Directional Movement indicators"""
        try:
            # Directional Movement
            data['dm_plus'] = np.where(
                (data['high'].diff() > data['low'].diff().abs()) & (data['high'].diff() > 0),
                data['high'].diff(),
                0
            )
            
            data['dm_minus'] = np.where(
                (data['low'].diff().abs() > data['high'].diff()) & (data['low'].diff() < 0),
                data['low'].diff().abs(),
                0
            )
            
            # True Range (already calculated)
            if 'atr' not in data.columns:
                data['tr'] = np.maximum(
                    data['high'] - data['low'],
                    np.maximum(
                        abs(data['high'] - data['close'].shift()),
                        abs(data['low'] - data['close'].shift())
                    )
                )
                data['atr'] = data['tr'].rolling(window=14).mean()
            
            # Directional Indicators
            data['plus_di'] = 100 * (data['dm_plus'].rolling(window=14).mean() / data['atr'])
            data['minus_di'] = 100 * (data['dm_minus'].rolling(window=14).mean() / data['atr'])
            
            # ADX
            data['dx'] = 100 * abs(data['plus_di'] - data['minus_di']) / (data['plus_di'] + data['minus_di'])
            data['adx'] = data['dx'].rolling(window=14).mean()
            
            # Clean up
            data.drop(columns=['dm_plus', 'dm_minus', 'dx'], inplace=True)
            if 'tr' in data.columns:
                data.drop(columns=['tr'], inplace=True)
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error calculating ADX: {e}")
            return data
    
    def _calculate_parabolic_sar(self, data: pd.DataFrame, af_start: float = 0.02, 
                                af_increment: float = 0.02, af_max: float = 0.2) -> pd.DataFrame:
        """Calculate Parabolic SAR"""
        try:
            if len(data) < 2:
                data['parabolic_sar'] = data['close']
                return data
            
            sar = np.zeros(len(data))
            trend = np.zeros(len(data))
            af = np.zeros(len(data))
            ep = np.zeros(len(data))
            
            # Initialize
            sar[0] = data['low'].iloc[0]
            trend[0] = 1  # 1 for uptrend, -1 for downtrend
            af[0] = af_start
            ep[0] = data['high'].iloc[0]
            
            for i in range(1, len(data)):
                # Previous values
                prev_sar = sar[i-1]
                prev_trend = trend[i-1]
                prev_af = af[i-1]
                prev_ep = ep[i-1]
                
                high = data['high'].iloc[i]
                low = data['low'].iloc[i]
                
                # Calculate SAR
                sar[i] = prev_sar + prev_af * (prev_ep - prev_sar)
                
                # Determine trend
                if prev_trend == 1:  # Uptrend
                    if low <= sar[i]:
                        # Trend reversal
                        trend[i] = -1
                        sar[i] = prev_ep
                        af[i] = af_start
                        ep[i] = low
                    else:
                        trend[i] = 1
                        if high > prev_ep:
                            ep[i] = high
                            af[i] = min(prev_af + af_increment, af_max)
                        else:
                            ep[i] = prev_ep
                            af[i] = prev_af
                        
                        # Adjust SAR
                        sar[i] = min(sar[i], data['low'].iloc[i-1], low)
                
                else:  # Downtrend
                    if high >= sar[i]:
                        # Trend reversal
                        trend[i] = 1
                        sar[i] = prev_ep
                        af[i] = af_start
                        ep[i] = high
                    else:
                        trend[i] = -1
                        if low < prev_ep:
                            ep[i] = low
                            af[i] = min(prev_af + af_increment, af_max)
                        else:
                            ep[i] = prev_ep
                            af[i] = prev_af
                        
                        # Adjust SAR
                        sar[i] = max(sar[i], data['high'].iloc[i-1], high)
            
            data['parabolic_sar'] = sar
            return data
            
        except Exception as e:
            self.logger.error(f"Error calculating Parabolic SAR: {e}")
            data['parabolic_sar'] = data['close']
            return data
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self.cache or cache_key not in self.last_update:
            return False
        
        age = (datetime.now() - self.last_update[cache_key]).total_seconds()
        return age < self.config.cache_duration
    
    def get_economic_calendar(self, date_range: int = 7) -> List[Dict]:
        """
        Get economic calendar events (simplified implementation)
        
        Args:
            date_range: Number of days to look ahead
            
        Returns:
            List of economic events
        """
        try:
            # This is a simplified implementation
            # In a real system, you would integrate with economic calendar APIs
            
            events = [
                {
                    'date': datetime.now() + timedelta(days=1),
                    'time': '08:30',
                    'currency': 'USD',
                    'event': 'Non-Farm Payrolls',
                    'impact': 'HIGH',
                    'forecast': '200K',
                    'previous': '180K'
                },
                {
                    'date': datetime.now() + timedelta(days=2),
                    'time': '14:00',
                    'currency': 'EUR',
                    'event': 'ECB Interest Rate Decision',
                    'impact': 'HIGH',
                    'forecast': '4.50%',
                    'previous': '4.50%'
                }
            ]
            
            return events
            
        except Exception as e:
            self.logger.error(f"Error getting economic calendar: {e}")
            return []

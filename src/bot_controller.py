"""
Bot Controller Module
Main controller that orchestrates all trading bot components
"""

import os
import time
import logging
import schedule
from datetime import datetime, timedelta
from typing import Dict, List
from colorama import Fore, Style
from tabulate import tabulate

class BotController:
    """Main controller for the forex trading bot"""
    
    def __init__(self, data_fetcher, ai_analyzer, trade_executor, risk_manager):
        """Initialize the bot controller with all components"""
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.data_fetcher = data_fetcher
        self.ai_analyzer = ai_analyzer
        self.trade_executor = trade_executor
        self.risk_manager = risk_manager
        
        # Load configuration
        self.currency_pairs = os.getenv('DEFAULT_CURRENCY_PAIR', 'EUR_USD').split(',')
        self.trading_start_hour = int(os.getenv('TRADING_START_HOUR', 8))
        self.trading_end_hour = int(os.getenv('TRADING_END_HOUR', 18))
        self.analysis_interval = int(os.getenv('ANALYSIS_INTERVAL_MINUTES', 15))
        
        # Bot state
        self.is_running = False
        self.last_analysis_time = None
        self.trade_history = []
        
        self.logger.info("Bot Controller initialized")
    
    def start(self):
        """Start the trading bot"""
        try:
            self.is_running = True
            self.logger.info("🚀 Starting Forex Trading Bot...")
            
            # Initial setup
            self._display_startup_info()
            
            # Schedule regular analysis
            schedule.every(self.analysis_interval).minutes.do(self._run_analysis_cycle)
            
            # Schedule daily summary
            schedule.every().day.at("18:00").do(self._generate_daily_summary)
            
            # Main loop
            while self.is_running:
                try:
                    # Run scheduled tasks
                    schedule.run_pending()
                    
                    # Check if we're in trading hours
                    if self._is_trading_hours():
                        # Run analysis if it's time
                        if self._should_run_analysis():
                            self._run_analysis_cycle()
                    
                    # Sleep for a short interval
                    time.sleep(30)  # Check every 30 seconds
                    
                except KeyboardInterrupt:
                    self.logger.info("Received stop signal")
                    break
                except Exception as e:
                    self.logger.error(f"Error in main loop: {e}")
                    time.sleep(60)  # Wait a minute before retrying
            
        except Exception as e:
            self.logger.error(f"Critical error in bot controller: {e}")
        finally:
            self._shutdown()
    
    def stop(self):
        """Stop the trading bot"""
        self.is_running = False
        self.logger.info("Bot stop requested")
    
    def _display_startup_info(self):
        """Display startup information"""
        try:
            # Get account summary
            account_summary = self.trade_executor.get_account_summary()
            
            # Get current positions
            positions = self.trade_executor.get_open_positions()
            
            # Display account info
            print(f"\n{Fore.CYAN}📊 Account Information{Style.RESET_ALL}")
            print("=" * 50)
            print(f"Account ID: {account_summary.get('account_id', 'N/A')}")
            print(f"Balance: {account_summary.get('balance', 0):,.2f} {account_summary.get('currency', 'USD')}")
            print(f"Unrealized P&L: {account_summary.get('unrealized_pl', 0):,.2f}")
            print(f"Open Positions: {len(positions)}")
            print(f"Margin Used: {account_summary.get('margin_used', 0):,.2f}")
            print(f"Margin Available: {account_summary.get('margin_available', 0):,.2f}")
            
            # Display trading configuration
            print(f"\n{Fore.YELLOW}⚙️ Trading Configuration{Style.RESET_ALL}")
            print("=" * 50)
            print(f"Currency Pairs: {', '.join(self.currency_pairs)}")
            print(f"Trading Hours: {self.trading_start_hour:02d}:00 - {self.trading_end_hour:02d}:00 UTC")
            print(f"Analysis Interval: {self.analysis_interval} minutes")
            print(f"Max Risk per Trade: {float(os.getenv('MAX_RISK_PER_TRADE', 2))}%")
            print(f"Max Daily Loss: {os.getenv('MAX_DAILY_LOSS', 100)} USD")
            print(f"Max Open Positions: {os.getenv('MAX_OPEN_POSITIONS', 3)}")
            
            # Display current positions if any
            if positions:
                print(f"\n{Fore.GREEN}📈 Current Positions{Style.RESET_ALL}")
                print("=" * 50)
                
                position_data = []
                for pos in positions:
                    if pos['long_units'] != 0:
                        position_data.append([
                            pos['instrument'],
                            'LONG',
                            f"{pos['long_units']:,.0f}",
                            f"{pos['long_avg_price']:.5f}",
                            f"{pos['long_pl']:,.2f}"
                        ])
                    if pos['short_units'] != 0:
                        position_data.append([
                            pos['instrument'],
                            'SHORT',
                            f"{abs(pos['short_units']):,.0f}",
                            f"{pos['short_avg_price']:.5f}",
                            f"{pos['short_pl']:,.2f}"
                        ])
                
                headers = ['Instrument', 'Side', 'Units', 'Avg Price', 'P&L']
                print(tabulate(position_data, headers=headers, tablefmt='grid'))
            
            print(f"\n{Fore.GREEN}✅ Bot is ready to trade!{Style.RESET_ALL}")
            
        except Exception as e:
            self.logger.error(f"Error displaying startup info: {e}")
    
    def _run_analysis_cycle(self):
        """Run a complete analysis and trading cycle"""
        try:
            self.logger.info("🔍 Starting analysis cycle...")
            
            # Get account summary
            account_summary = self.trade_executor.get_account_summary()
            current_positions = self.trade_executor.get_open_positions()
            
            # Check if trading is allowed
            trading_allowed, reason = self.risk_manager.check_trading_allowed(
                account_summary, current_positions
            )
            
            if not trading_allowed:
                self.logger.warning(f"Trading not allowed: {reason}")
                return
            
            # Analyze each currency pair
            for pair in self.currency_pairs:
                try:
                    self._analyze_and_trade_pair(pair, account_summary, current_positions)
                except Exception as e:
                    self.logger.error(f"Error analyzing {pair}: {e}")
            
            # Update last analysis time
            self.last_analysis_time = datetime.now()
            
            # Display current status
            self._display_status_update(account_summary, current_positions)
            
        except Exception as e:
            self.logger.error(f"Error in analysis cycle: {e}")
    
    def _analyze_and_trade_pair(self, pair: str, account_summary: Dict, current_positions: List):
        """Analyze and potentially trade a currency pair"""
        try:
            self.logger.info(f"Analyzing {pair}...")
            
            # Check if we already have a position in this pair
            existing_position = None
            for pos in current_positions:
                if pos['instrument'] == pair:
                    existing_position = pos
                    break
            
            # Get market data
            market_data = self.data_fetcher.get_market_analysis_data(pair)
            market_data['instrument'] = pair
            
            # Get AI analysis
            ai_signal = self.ai_analyzer.analyze_market_data(market_data)
            ai_signal['instrument'] = pair
            
            self.logger.info(f"{pair} AI Signal: {ai_signal['signal']} (Confidence: {ai_signal['confidence']:.2f})")
            
            # If we have an existing position, check if we should close it
            if existing_position:
                self._manage_existing_position(existing_position, ai_signal, market_data)
            
            # If signal is not HOLD and we don't have a conflicting position, consider opening new trade
            elif ai_signal['signal'] in ['BUY', 'SELL']:
                self._execute_new_trade(ai_signal, account_summary, current_positions)
            
        except Exception as e:
            self.logger.error(f"Error analyzing pair {pair}: {e}")
    
    def _manage_existing_position(self, position: Dict, ai_signal: Dict, market_data: Dict):
        """Manage an existing position based on AI signal"""
        try:
            instrument = position['instrument']
            current_price = market_data['current_price']['mid']
            
            # Determine position direction
            if position['long_units'] > 0:
                position_side = 'LONG'
                position_pl = position['long_pl']
            else:
                position_side = 'SHORT'
                position_pl = position['short_pl']
            
            # Check if AI suggests closing the position
            should_close = False
            close_reason = ""
            
            # Close if AI signal is opposite to position
            if (position_side == 'LONG' and ai_signal['signal'] == 'SELL') or \
               (position_side == 'SHORT' and ai_signal['signal'] == 'BUY'):
                should_close = True
                close_reason = f"AI signal changed to {ai_signal['signal']}"
            
            # Close if confidence is very low
            elif ai_signal['confidence'] < 0.3:
                should_close = True
                close_reason = "AI confidence too low"
            
            # Close if position is significantly profitable (take some profit)
            elif position_pl > 50:  # More than $50 profit
                should_close = True
                close_reason = "Taking profit"
            
            if should_close:
                self.logger.info(f"Closing position for {instrument}: {close_reason}")
                result = self.trade_executor.close_position(instrument)
                
                if result['success']:
                    self.logger.info(f"Position closed successfully for {instrument}")
                    self.risk_manager.update_daily_stats(result)
                    self._log_trade(instrument, 'CLOSE', result)
                else:
                    self.logger.error(f"Failed to close position for {instrument}: {result.get('error')}")
            
        except Exception as e:
            self.logger.error(f"Error managing existing position: {e}")
    
    def _execute_new_trade(self, ai_signal: Dict, account_summary: Dict, current_positions: List):
        """Execute a new trade based on AI signal"""
        try:
            # Validate the signal
            is_valid, validated_signal = self.risk_manager.validate_trade_signal(
                ai_signal, account_summary, current_positions
            )
            
            if not is_valid:
                self.logger.warning(f"Trade signal rejected: {validated_signal.get('error')}")
                return
            
            # Prepare trade parameters
            instrument = validated_signal['instrument']
            signal = validated_signal['signal']
            position_size = validated_signal['position_size']
            entry_price = validated_signal['entry_price']
            stop_loss = validated_signal.get('stop_loss')
            take_profit = validated_signal.get('take_profit')
            
            # Convert signal to units (positive for BUY, negative for SELL)
            units = position_size if signal == 'BUY' else -position_size
            
            self.logger.info(f"Executing {signal} order for {instrument}: {abs(units)} units")
            
            # Place the order
            result = self.trade_executor.place_market_order(
                instrument=instrument,
                units=units,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            if result['success']:
                self.logger.info(f"Trade executed successfully: {signal} {abs(units)} {instrument}")
                self.risk_manager.update_daily_stats(result)
                self._log_trade(instrument, signal, result, validated_signal)
                
                # Display trade confirmation
                print(f"\n{Fore.GREEN}✅ Trade Executed{Style.RESET_ALL}")
                print(f"Instrument: {instrument}")
                print(f"Action: {signal}")
                print(f"Units: {abs(units):,}")
                print(f"Price: {result['price']:.5f}")
                print(f"Stop Loss: {stop_loss:.5f}" if stop_loss else "Stop Loss: Not set")
                print(f"Take Profit: {take_profit:.5f}" if take_profit else "Take Profit: Not set")
                print(f"Risk: {validated_signal['risk_info']['risk_percentage']:.2f}%")
                
            else:
                self.logger.error(f"Trade execution failed: {result.get('error')}")
                
        except Exception as e:
            self.logger.error(f"Error executing new trade: {e}")
    
    def _log_trade(self, instrument: str, action: str, result: Dict, signal: Dict = None):
        """Log trade details for analysis"""
        trade_log = {
            'timestamp': datetime.now().isoformat(),
            'instrument': instrument,
            'action': action,
            'result': result,
            'signal': signal
        }
        
        self.trade_history.append(trade_log)
        
        # Keep only last 100 trades in memory
        if len(self.trade_history) > 100:
            self.trade_history = self.trade_history[-100:]
    
    def _display_status_update(self, account_summary: Dict, current_positions: List):
        """Display current status update"""
        try:
            risk_summary = self.risk_manager.get_risk_summary(account_summary)
            
            print(f"\n{Fore.CYAN}📊 Status Update - {datetime.now().strftime('%H:%M:%S')}{Style.RESET_ALL}")
            print(f"Balance: {account_summary.get('balance', 0):,.2f} | " +
                  f"Unrealized P&L: {account_summary.get('unrealized_pl', 0):+,.2f} | " +
                  f"Daily Trades: {risk_summary.get('daily_trades', 0)} | " +
                  f"Open Positions: {len(current_positions)}")
            
        except Exception as e:
            self.logger.error(f"Error displaying status update: {e}")
    
    def _is_trading_hours(self) -> bool:
        """Check if current time is within trading hours"""
        current_hour = datetime.now().hour
        return self.trading_start_hour <= current_hour < self.trading_end_hour
    
    def _should_run_analysis(self) -> bool:
        """Check if it's time to run analysis"""
        if self.last_analysis_time is None:
            return True
        
        time_since_last = datetime.now() - self.last_analysis_time
        return time_since_last >= timedelta(minutes=self.analysis_interval)
    
    def _generate_daily_summary(self):
        """Generate and display daily trading summary"""
        try:
            account_summary = self.trade_executor.get_account_summary()
            risk_summary = self.risk_manager.get_risk_summary(account_summary)
            
            print(f"\n{Fore.YELLOW}📈 Daily Trading Summary{Style.RESET_ALL}")
            print("=" * 50)
            print(f"Date: {datetime.now().strftime('%Y-%m-%d')}")
            print(f"Total Trades: {risk_summary.get('daily_trades', 0)}")
            print(f"Daily P&L: {risk_summary.get('daily_pl', 0):+,.2f}")
            print(f"Daily P&L %: {risk_summary.get('daily_pl_percentage', 0):+.2f}%")
            print(f"Max Drawdown: {risk_summary.get('max_drawdown', 0):,.2f}")
            print(f"Account Balance: {account_summary.get('balance', 0):,.2f}")
            
        except Exception as e:
            self.logger.error(f"Error generating daily summary: {e}")
    
    def _shutdown(self):
        """Shutdown the bot gracefully"""
        try:
            self.logger.info("🛑 Shutting down trading bot...")
            
            # Generate final summary
            self._generate_daily_summary()
            
            print(f"\n{Fore.YELLOW}👋 Bot shutdown complete. Thank you for using the Forex Trading Bot!{Style.RESET_ALL}")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

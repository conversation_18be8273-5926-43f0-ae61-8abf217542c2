"""
AI Analyzer Module
Uses OpenAI GPT to analyze forex market data and generate trading signals
"""

import os
import logging
import json
from typing import Dict, Optional
from openai import OpenAI
from datetime import datetime

class AIAnalyzer:
    """AI-powered forex market analyzer using OpenAI GPT"""
    
    def __init__(self):
        """Initialize the AI analyzer with OpenAI credentials"""
        self.logger = logging.getLogger(__name__)
        
        # Get OpenAI credentials
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        
        # Initialize OpenAI client
        self.client = OpenAI(api_key=api_key)
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4')
        
        self.logger.info(f"AI Analyzer initialized with model: {self.model}")
    
    def analyze_market_data(self, market_data: Dict) -> Dict:
        """
        Analyze market data and generate trading signal
        
        Args:
            market_data: Market analysis data from DataFetcher
            
        Returns:
            Dict with trading signal and analysis
        """
        try:
            # Prepare the analysis prompt
            prompt = self._create_analysis_prompt(market_data)
            
            # Get AI analysis
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=1000
            )
            
            # Parse the response
            ai_response = response.choices[0].message.content
            analysis_result = self._parse_ai_response(ai_response, market_data)
            
            self.logger.info(f"AI analysis completed for {market_data['instrument']}: {analysis_result['signal']}")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Error in AI analysis: {e}")
            # Return neutral signal on error
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'reasoning': f"Analysis failed: {str(e)}",
                'entry_price': market_data['current_price']['mid'],
                'stop_loss': None,
                'take_profit': None,
                'position_size': 0
            }
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the AI"""
        return """You are an expert forex trader and technical analyst. Your job is to analyze market data and provide trading signals.

IMPORTANT INSTRUCTIONS:
1. Analyze the provided technical indicators carefully
2. Consider multiple timeframes (H1, H4, Daily)
3. Provide a clear trading signal: BUY, SELL, or HOLD
4. Give a confidence level from 0.0 to 1.0
5. Explain your reasoning clearly
6. Suggest entry price, stop loss, and take profit levels
7. Be conservative - only suggest trades with good risk/reward ratio

RESPONSE FORMAT:
Signal: [BUY/SELL/HOLD]
Confidence: [0.0-1.0]
Entry Price: [price level]
Stop Loss: [price level or null]
Take Profit: [price level or null]
Position Size: [1-3, where 1=small, 2=medium, 3=large]
Reasoning: [detailed explanation]

RISK MANAGEMENT:
- Only suggest BUY/SELL if confidence > 0.6
- Always include stop loss for BUY/SELL signals
- Risk/reward ratio should be at least 1:1.5
- Consider market volatility and spread"""

    def _create_analysis_prompt(self, market_data: Dict) -> str:
        """Create the analysis prompt with market data"""
        
        current_price = market_data['current_price']
        h1 = market_data['h1_analysis']
        h4 = market_data['h4_analysis']
        daily = market_data['daily_analysis']
        
        prompt = f"""
FOREX MARKET ANALYSIS REQUEST

Instrument: {market_data['instrument']}
Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

CURRENT MARKET DATA:
- Current Bid: {current_price['bid']:.5f}
- Current Ask: {current_price['ask']:.5f}
- Current Mid: {current_price['mid']:.5f}
- Spread: {current_price['spread']:.5f}

TECHNICAL ANALYSIS (Multiple Timeframes):

1-HOUR TIMEFRAME:
- Current Price: {h1['close']:.5f}
- SMA 20: {h1['sma_20']:.5f}
- SMA 50: {h1['sma_50']:.5f}
- RSI: {h1['rsi']:.2f}
- MACD: {h1['macd']:.5f}
- MACD Signal: {h1['macd_signal']:.5f}
- Bollinger Band Position: {h1['bb_position']:.3f} (0=lower band, 1=upper band)

4-HOUR TIMEFRAME:
- Current Price: {h4['close']:.5f}
- SMA 20: {h4['sma_20']:.5f}
- SMA 50: {h4['sma_50']:.5f}
- RSI: {h4['rsi']:.2f}
- MACD: {h4['macd']:.5f}
- Bollinger Band Position: {h4['bb_position']:.3f}

DAILY TIMEFRAME:
- Current Price: {daily['close']:.5f}
- SMA 20: {daily['sma_20']:.5f}
- SMA 50: {daily['sma_50']:.5f}
- RSI: {daily['rsi']:.2f}
- Bollinger Band Position: {daily['bb_position']:.3f}

ANALYSIS GUIDELINES:
- RSI > 70 = Overbought, RSI < 30 = Oversold
- MACD above signal = Bullish momentum, below = Bearish momentum
- Price above SMA = Uptrend, below = Downtrend
- BB Position > 0.8 = Near upper band, < 0.2 = Near lower band

Please provide your trading recommendation based on this technical analysis.
"""
        
        return prompt
    
    def _parse_ai_response(self, ai_response: str, market_data: Dict) -> Dict:
        """Parse the AI response into structured data"""
        
        try:
            lines = ai_response.strip().split('\n')
            result = {
                'signal': 'HOLD',
                'confidence': 0.0,
                'reasoning': ai_response,
                'entry_price': market_data['current_price']['mid'],
                'stop_loss': None,
                'take_profit': None,
                'position_size': 1,
                'raw_response': ai_response
            }
            
            # Parse each line
            for line in lines:
                line = line.strip()
                if line.startswith('Signal:'):
                    signal = line.split(':', 1)[1].strip().upper()
                    if signal in ['BUY', 'SELL', 'HOLD']:
                        result['signal'] = signal
                
                elif line.startswith('Confidence:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                        result['confidence'] = max(0.0, min(1.0, confidence))
                    except ValueError:
                        pass
                
                elif line.startswith('Entry Price:'):
                    try:
                        entry = line.split(':', 1)[1].strip()
                        if entry.lower() not in ['null', 'none', '']:
                            result['entry_price'] = float(entry)
                    except ValueError:
                        pass
                
                elif line.startswith('Stop Loss:'):
                    try:
                        stop_loss = line.split(':', 1)[1].strip()
                        if stop_loss.lower() not in ['null', 'none', '']:
                            result['stop_loss'] = float(stop_loss)
                    except ValueError:
                        pass
                
                elif line.startswith('Take Profit:'):
                    try:
                        take_profit = line.split(':', 1)[1].strip()
                        if take_profit.lower() not in ['null', 'none', '']:
                            result['take_profit'] = float(take_profit)
                    except ValueError:
                        pass
                
                elif line.startswith('Position Size:'):
                    try:
                        size = int(line.split(':', 1)[1].strip())
                        result['position_size'] = max(1, min(3, size))
                    except ValueError:
                        pass
                
                elif line.startswith('Reasoning:'):
                    reasoning = line.split(':', 1)[1].strip()
                    if reasoning:
                        result['reasoning'] = reasoning
            
            # Validate the result
            result = self._validate_analysis_result(result, market_data)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error parsing AI response: {e}")
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'reasoning': f"Failed to parse AI response: {str(e)}",
                'entry_price': market_data['current_price']['mid'],
                'stop_loss': None,
                'take_profit': None,
                'position_size': 1,
                'raw_response': ai_response
            }
    
    def _validate_analysis_result(self, result: Dict, market_data: Dict) -> Dict:
        """Validate and adjust the analysis result"""
        
        current_price = market_data['current_price']['mid']
        spread = market_data['current_price']['spread']
        
        # If confidence is too low, change to HOLD
        if result['confidence'] < 0.6 and result['signal'] in ['BUY', 'SELL']:
            result['signal'] = 'HOLD'
            result['reasoning'] += " (Changed to HOLD due to low confidence)"
        
        # Validate stop loss and take profit levels
        if result['signal'] == 'BUY':
            # For BUY: stop loss should be below entry, take profit above
            if result['stop_loss'] and result['stop_loss'] >= result['entry_price']:
                result['stop_loss'] = result['entry_price'] - (spread * 20)  # 20 spreads below
            
            if result['take_profit'] and result['take_profit'] <= result['entry_price']:
                result['take_profit'] = result['entry_price'] + (spread * 30)  # 30 spreads above
        
        elif result['signal'] == 'SELL':
            # For SELL: stop loss should be above entry, take profit below
            if result['stop_loss'] and result['stop_loss'] <= result['entry_price']:
                result['stop_loss'] = result['entry_price'] + (spread * 20)  # 20 spreads above
            
            if result['take_profit'] and result['take_profit'] >= result['entry_price']:
                result['take_profit'] = result['entry_price'] - (spread * 30)  # 30 spreads below
        
        # Ensure entry price is reasonable (within 5 spreads of current price)
        max_entry_diff = spread * 5
        if abs(result['entry_price'] - current_price) > max_entry_diff:
            result['entry_price'] = current_price
        
        return result
    
    def get_market_sentiment(self, instruments: list) -> Dict:
        """
        Get overall market sentiment for multiple instruments
        
        Args:
            instruments: List of currency pairs to analyze
            
        Returns:
            Dict with market sentiment analysis
        """
        try:
            prompt = f"""
Provide a brief overall forex market sentiment analysis for these currency pairs: {', '.join(instruments)}

Consider:
- Major economic events
- Market trends
- Risk sentiment (risk-on vs risk-off)
- USD strength/weakness
- General market conditions

Provide a 2-3 sentence summary of current market sentiment.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a forex market analyst. Provide concise market sentiment analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.5,
                max_tokens=200
            )
            
            sentiment = response.choices[0].message.content
            
            return {
                'sentiment': sentiment,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting market sentiment: {e}")
            return {
                'sentiment': "Unable to determine market sentiment due to analysis error.",
                'timestamp': datetime.now().isoformat()
            }

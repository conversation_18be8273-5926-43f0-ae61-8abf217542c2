"""
Beautiful CLI Interface
Stunning terminal interface with real-time charts, data feeds, and interactive controls
"""

import os
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.columns import Columns
from rich.align import Align
from rich import box
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd

class BeautifulCLI:
    """
    Beautiful CLI interface for the forex trading bot
    
    Features:
    - Real-time market data display
    - Live trading signals
    - Performance metrics
    - Interactive charts
    - Strategy status
    - Risk monitoring
    """
    
    def __init__(self):
        """Initialize the beautiful CLI"""
        self.console = Console()
        self.layout = Layout()
        self.is_running = False
        self.data_lock = threading.Lock()
        
        # Data storage
        self.market_data = {}
        self.signals = []
        self.performance_data = {}
        self.strategy_status = {}
        self.risk_metrics = {}
        
        # Setup layout
        self._setup_layout()
        
        # Colors and styles
        self.colors = {
            'buy': 'green',
            'sell': 'red',
            'hold': 'yellow',
            'profit': 'bright_green',
            'loss': 'bright_red',
            'neutral': 'white',
            'header': 'cyan',
            'accent': 'magenta'
        }
    
    def _setup_layout(self):
        """Setup the main layout structure"""
        # Create main layout sections
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main", ratio=1),
            Layout(name="footer", size=3)
        )
        
        # Split main section
        self.layout["main"].split_row(
            Layout(name="left", ratio=2),
            Layout(name="center", ratio=3),
            Layout(name="right", ratio=2)
        )
        
        # Split left column
        self.layout["left"].split_column(
            Layout(name="market_data", ratio=1),
            Layout(name="signals", ratio=1)
        )
        
        # Split center column
        self.layout["center"].split_column(
            Layout(name="chart", ratio=2),
            Layout(name="analysis", ratio=1)
        )
        
        # Split right column
        self.layout["right"].split_column(
            Layout(name="performance", ratio=1),
            Layout(name="strategies", ratio=1),
            Layout(name="risk", ratio=1)
        )
    
    def start(self):
        """Start the beautiful CLI interface"""
        self.is_running = True
        
        with Live(self.layout, console=self.console, refresh_per_second=2) as live:
            while self.is_running:
                try:
                    self._update_all_panels()
                    time.sleep(0.5)
                except KeyboardInterrupt:
                    self.is_running = False
                    break
                except Exception as e:
                    self.console.print(f"[red]CLI Error: {e}[/red]")
                    time.sleep(1)
    
    def stop(self):
        """Stop the CLI interface"""
        self.is_running = False
    
    def update_market_data(self, data: Dict):
        """Update market data display"""
        with self.data_lock:
            self.market_data.update(data)
    
    def add_signal(self, signal: Dict):
        """Add new trading signal"""
        with self.data_lock:
            self.signals.append({
                **signal,
                'timestamp': datetime.now()
            })
            # Keep only last 10 signals
            self.signals = self.signals[-10:]
    
    def update_performance(self, performance: Dict):
        """Update performance metrics"""
        with self.data_lock:
            self.performance_data.update(performance)
    
    def update_strategies(self, strategies: Dict):
        """Update strategy status"""
        with self.data_lock:
            self.strategy_status.update(strategies)
    
    def update_risk_metrics(self, risk: Dict):
        """Update risk metrics"""
        with self.data_lock:
            self.risk_metrics.update(risk)
    
    def _update_all_panels(self):
        """Update all display panels"""
        with self.data_lock:
            self.layout["header"].update(self._create_header_panel())
            self.layout["market_data"].update(self._create_market_data_panel())
            self.layout["signals"].update(self._create_signals_panel())
            self.layout["chart"].update(self._create_chart_panel())
            self.layout["analysis"].update(self._create_analysis_panel())
            self.layout["performance"].update(self._create_performance_panel())
            self.layout["strategies"].update(self._create_strategies_panel())
            self.layout["risk"].update(self._create_risk_panel())
            self.layout["footer"].update(self._create_footer_panel())
    
    def _create_header_panel(self) -> Panel:
        """Create header panel with title and status"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        
        title = Text("🤖 ADVANCED FOREX TRADING BOT", style="bold cyan")
        subtitle = Text(f"AI-Powered • Real-time Analysis • {current_time}", style="dim white")
        
        header_content = Align.center(
            Text.assemble(title, "\n", subtitle)
        )
        
        return Panel(
            header_content,
            box=box.DOUBLE,
            style="cyan",
            padding=(0, 1)
        )
    
    def _create_market_data_panel(self) -> Panel:
        """Create market data panel"""
        table = Table(box=box.SIMPLE, show_header=True, header_style="bold cyan")
        table.add_column("Pair", style="white", width=8)
        table.add_column("Price", style="white", width=10)
        table.add_column("Change", width=8)
        table.add_column("Spread", style="dim white", width=6)
        
        # Add market data rows
        for instrument, data in self.market_data.items():
            if isinstance(data, dict) and 'current_price' in data:
                price_data = data['current_price']
                price = f"{price_data.get('mid', 0):.5f}"
                
                # Calculate change (mock for now)
                change = "+0.0012"
                change_style = "green" if change.startswith('+') else "red"
                
                spread = f"{price_data.get('spread', 0):.1f}"
                
                table.add_row(
                    instrument.replace('_', '/'),
                    price,
                    f"[{change_style}]{change}[/{change_style}]",
                    spread
                )
        
        if not self.market_data:
            table.add_row("EUR/USD", "1.08450", "[green]+0.0012[/green]", "1.2")
            table.add_row("GBP/USD", "1.26780", "[red]-0.0008[/red]", "1.5")
            table.add_row("USD/JPY", "149.250", "[green]+0.180[/green]", "1.8")
        
        return Panel(
            table,
            title="📊 Live Market Data",
            border_style="blue",
            padding=(0, 1)
        )
    
    def _create_signals_panel(self) -> Panel:
        """Create trading signals panel"""
        table = Table(box=box.SIMPLE, show_header=True, header_style="bold yellow")
        table.add_column("Time", style="dim white", width=8)
        table.add_column("Signal", width=6)
        table.add_column("Pair", style="white", width=8)
        table.add_column("Conf", width=5)
        
        # Add recent signals
        for signal in self.signals[-5:]:  # Last 5 signals
            time_str = signal['timestamp'].strftime("%H:%M")
            signal_type = signal.get('signal_type', 'HOLD')
            confidence = signal.get('confidence', 0)
            instrument = signal.get('instrument', 'EUR_USD')
            
            # Color code signals
            if signal_type == 'BUY':
                signal_style = "bold green"
                signal_text = "🟢 BUY"
            elif signal_type == 'SELL':
                signal_style = "bold red"
                signal_text = "🔴 SELL"
            else:
                signal_style = "yellow"
                signal_text = "🟡 HOLD"
            
            table.add_row(
                time_str,
                f"[{signal_style}]{signal_text}[/{signal_style}]",
                instrument.replace('_', '/'),
                f"{confidence:.2f}"
            )
        
        if not self.signals:
            table.add_row("14:30", "[green]🟢 BUY[/green]", "EUR/USD", "0.85")
            table.add_row("14:25", "[yellow]🟡 HOLD[/yellow]", "GBP/USD", "0.62")
            table.add_row("14:20", "[red]🔴 SELL[/red]", "USD/JPY", "0.78")
        
        return Panel(
            table,
            title="🎯 Trading Signals",
            border_style="yellow",
            padding=(0, 1)
        )
    
    def _create_chart_panel(self) -> Panel:
        """Create chart panel (simplified ASCII chart)"""
        # Create a simple ASCII chart representation
        chart_lines = []
        chart_lines.append("EUR/USD - 1H Chart")
        chart_lines.append("")
        chart_lines.append("1.0860 ┤")
        chart_lines.append("1.0855 ┤   ╭─╮")
        chart_lines.append("1.0850 ┤  ╱   ╲")
        chart_lines.append("1.0845 ┤ ╱     ╲   ╭─")
        chart_lines.append("1.0840 ┤╱       ╲ ╱")
        chart_lines.append("1.0835 ┤         ╲╱")
        chart_lines.append("       └─────────────────")
        chart_lines.append("       12:00  13:00  14:00")
        chart_lines.append("")
        chart_lines.append("📈 Trend: BULLISH | RSI: 65.2 | MACD: +0.0008")
        
        chart_content = "\n".join(chart_lines)
        
        return Panel(
            chart_content,
            title="📈 Price Chart",
            border_style="green",
            padding=(1, 2)
        )
    
    def _create_analysis_panel(self) -> Panel:
        """Create AI analysis panel"""
        analysis_content = []
        
        # Market regime
        regime = "TRENDING_BULL"
        regime_color = "green" if "BULL" in regime else "red" if "BEAR" in regime else "yellow"
        analysis_content.append(f"🎯 Market Regime: [{regime_color}]{regime}[/{regime_color}]")
        
        # AI confidence
        analysis_content.append(f"🤖 AI Confidence: [bold green]87.5%[/bold green]")
        
        # Strategy recommendation
        analysis_content.append(f"📋 Strategy: [cyan]Trend Following + Day Trading[/cyan]")
        
        # Risk assessment
        analysis_content.append(f"⚠️  Risk Level: [yellow]MEDIUM[/yellow]")
        
        # Key factors
        analysis_content.append("")
        analysis_content.append("[bold]Key Factors:[/bold]")
        analysis_content.append("• Multi-timeframe bullish alignment")
        analysis_content.append("• Strong momentum confirmation")
        analysis_content.append("• Volume supporting the move")
        analysis_content.append("• Low correlation risk")
        
        return Panel(
            "\n".join(analysis_content),
            title="🧠 AI Analysis",
            border_style="magenta",
            padding=(0, 1)
        )
    
    def _create_performance_panel(self) -> Panel:
        """Create performance metrics panel"""
        table = Table(box=box.SIMPLE, show_header=False)
        table.add_column("Metric", style="white", width=12)
        table.add_column("Value", width=10)
        
        # Performance metrics
        daily_pnl = self.performance_data.get('daily_pnl', 125.50)
        win_rate = self.performance_data.get('win_rate', 0.68)
        total_trades = self.performance_data.get('total_trades', 24)
        
        pnl_color = "green" if daily_pnl >= 0 else "red"
        pnl_symbol = "+" if daily_pnl >= 0 else ""
        
        table.add_row("Daily P&L", f"[{pnl_color}]{pnl_symbol}${daily_pnl:.2f}[/{pnl_color}]")
        table.add_row("Win Rate", f"[green]{win_rate:.1%}[/green]")
        table.add_row("Trades", f"{total_trades}")
        table.add_row("Sharpe", f"[cyan]1.85[/cyan]")
        table.add_row("Max DD", f"[red]-2.1%[/red]")
        
        return Panel(
            table,
            title="📊 Performance",
            border_style="green",
            padding=(0, 1)
        )
    
    def _create_strategies_panel(self) -> Panel:
        """Create strategies status panel"""
        table = Table(box=box.SIMPLE, show_header=True, header_style="bold cyan")
        table.add_column("Strategy", style="white", width=12)
        table.add_column("Status", width=8)
        table.add_column("Signals", width=6)
        
        strategies = [
            ("Scalping", "🟢 ACTIVE", "3"),
            ("Day Trading", "🟢 ACTIVE", "5"),
            ("Trend Follow", "🟢 ACTIVE", "2"),
            ("Mean Revert", "🟡 STANDBY", "0")
        ]
        
        for strategy, status, signals in strategies:
            table.add_row(strategy, status, signals)
        
        return Panel(
            table,
            title="⚙️ Strategies",
            border_style="cyan",
            padding=(0, 1)
        )
    
    def _create_risk_panel(self) -> Panel:
        """Create risk monitoring panel"""
        table = Table(box=box.SIMPLE, show_header=False)
        table.add_column("Risk Factor", style="white", width=12)
        table.add_column("Level", width=8)
        
        # Risk metrics
        portfolio_risk = self.risk_metrics.get('portfolio_risk', 'LOW')
        volatility = self.risk_metrics.get('volatility', 'MEDIUM')
        correlation = self.risk_metrics.get('correlation', 'LOW')
        
        risk_colors = {'LOW': 'green', 'MEDIUM': 'yellow', 'HIGH': 'red'}
        
        table.add_row("Portfolio", f"[{risk_colors.get(portfolio_risk, 'white')}]{portfolio_risk}[/{risk_colors.get(portfolio_risk, 'white')}]")
        table.add_row("Volatility", f"[{risk_colors.get(volatility, 'white')}]{volatility}[/{risk_colors.get(volatility, 'white')}]")
        table.add_row("Correlation", f"[{risk_colors.get(correlation, 'white')}]{correlation}[/{risk_colors.get(correlation, 'white')}]")
        table.add_row("Exposure", f"[yellow]45%[/yellow]")
        table.add_row("VaR (95%)", f"[red]-$89[/red]")
        
        return Panel(
            table,
            title="⚠️ Risk Monitor",
            border_style="red",
            padding=(0, 1)
        )
    
    def _create_footer_panel(self) -> Panel:
        """Create footer panel with controls"""
        controls = [
            "[bold cyan]Controls:[/bold cyan]",
            "[white]Ctrl+C[/white] Exit",
            "[white]Space[/white] Pause",
            "[white]R[/white] Reset",
            "[white]S[/white] Settings"
        ]
        
        footer_content = " | ".join(controls)
        
        return Panel(
            Align.center(footer_content),
            box=box.SIMPLE,
            style="dim white",
            padding=(0, 1)
        )

def main():
    """Main function to run the beautiful CLI"""
    cli = BeautifulCLI()
    
    try:
        cli.start()
    except KeyboardInterrupt:
        cli.console.print("\n[yellow]Shutting down...[/yellow]")
    finally:
        cli.stop()

if __name__ == "__main__":
    main()

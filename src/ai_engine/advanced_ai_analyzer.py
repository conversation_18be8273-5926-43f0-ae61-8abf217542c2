"""
Advanced AI Analysis Engine
Multi-model AI system for comprehensive market analysis and strategy selection
"""

import os
import logging
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from openai import OpenAI
from dataclasses import dataclass
from enum import Enum

class MarketRegime(Enum):
    """Market regime classification"""
    TRENDING_BULL = "trending_bull"
    TRENDING_BEAR = "trending_bear"
    RANGING = "ranging"
    VOLATILE = "volatile"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"

@dataclass
class AIAnalysisResult:
    """Comprehensive AI analysis result"""
    primary_signal: str  # BUY, SELL, HOLD
    confidence: float  # 0.0 to 1.0
    market_regime: MarketRegime
    strategy_recommendations: List[str]
    risk_assessment: Dict
    entry_zones: List[float]
    exit_zones: List[float]
    reasoning: str
    technical_summary: Dict
    sentiment_score: float
    volatility_forecast: float
    probability_distribution: Dict
    metadata: Dict

class AdvancedAIAnalyzer:
    """
    Advanced AI-powered market analyzer with multiple analysis models
    
    Features:
    - Multi-model ensemble analysis
    - Market regime detection
    - Pattern recognition
    - Sentiment analysis
    - Risk assessment
    - Strategy selection
    - Probability forecasting
    """
    
    def __init__(self):
        """Initialize the advanced AI analyzer"""
        self.logger = logging.getLogger(__name__)
        
        # Initialize OpenAI client
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OpenAI API key not found in environment variables")
        
        self.client = OpenAI(api_key=api_key)
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4')
        
        # Analysis models configuration
        self.models = {
            'technical_analyst': {
                'role': 'Expert Technical Analyst',
                'focus': 'Chart patterns, indicators, support/resistance',
                'weight': 0.3
            },
            'quantitative_analyst': {
                'role': 'Quantitative Market Analyst',
                'focus': 'Statistical analysis, probability, risk metrics',
                'weight': 0.25
            },
            'macro_analyst': {
                'role': 'Macro Economic Analyst',
                'focus': 'Economic fundamentals, central bank policy, global trends',
                'weight': 0.2
            },
            'sentiment_analyst': {
                'role': 'Market Sentiment Analyst',
                'focus': 'Market psychology, positioning, sentiment indicators',
                'weight': 0.15
            },
            'risk_manager': {
                'role': 'Risk Management Specialist',
                'focus': 'Risk assessment, position sizing, scenario analysis',
                'weight': 0.1
            }
        }
        
        self.logger.info(f"Advanced AI Analyzer initialized with {len(self.models)} analysis models")
    
    def comprehensive_analysis(self, market_data: Dict, instruments: List[str] = None) -> AIAnalysisResult:
        """
        Perform comprehensive multi-model AI analysis
        
        Args:
            market_data: Complete market data with multiple timeframes
            instruments: List of instruments to analyze (for correlation)
            
        Returns:
            Comprehensive AI analysis result
        """
        try:
            instrument = market_data.get('instrument', 'EUR_USD')
            self.logger.info(f"Starting comprehensive AI analysis for {instrument}")
            
            # Step 1: Market regime detection
            market_regime = self._detect_market_regime(market_data)
            
            # Step 2: Multi-model analysis
            model_results = self._run_multi_model_analysis(market_data, market_regime)
            
            # Step 3: Pattern recognition
            patterns = self._analyze_patterns(market_data)
            
            # Step 4: Sentiment analysis
            sentiment = self._analyze_market_sentiment(market_data, instruments)
            
            # Step 5: Risk assessment
            risk_assessment = self._assess_risk(market_data, market_regime)
            
            # Step 6: Strategy selection
            strategy_recommendations = self._select_strategies(market_regime, model_results, patterns)
            
            # Step 7: Probability forecasting
            probability_dist = self._forecast_probabilities(market_data, model_results)
            
            # Step 8: Ensemble decision making
            final_result = self._ensemble_decision(
                model_results, patterns, sentiment, risk_assessment, 
                strategy_recommendations, probability_dist, market_regime
            )
            
            # Create comprehensive result
            result = AIAnalysisResult(
                primary_signal=final_result['signal'],
                confidence=final_result['confidence'],
                market_regime=market_regime,
                strategy_recommendations=strategy_recommendations,
                risk_assessment=risk_assessment,
                entry_zones=final_result.get('entry_zones', []),
                exit_zones=final_result.get('exit_zones', []),
                reasoning=final_result['reasoning'],
                technical_summary=patterns,
                sentiment_score=sentiment['score'],
                volatility_forecast=risk_assessment.get('volatility_forecast', 0.5),
                probability_distribution=probability_dist,
                metadata={
                    'analysis_timestamp': datetime.now().isoformat(),
                    'model_results': model_results,
                    'market_regime': market_regime.value,
                    'instruments_analyzed': instruments or [instrument]
                }
            )
            
            self.logger.info(f"Comprehensive analysis completed: {result.primary_signal} "
                           f"(confidence: {result.confidence:.2f})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive analysis: {e}")
            return self._create_fallback_result(market_data)
    
    def _detect_market_regime(self, market_data: Dict) -> MarketRegime:
        """Detect current market regime using AI and technical analysis"""
        try:
            # Get multi-timeframe data
            h1_data = market_data.get('H1', pd.DataFrame())
            h4_data = market_data.get('H4', pd.DataFrame())
            d1_data = market_data.get('D1', pd.DataFrame())
            
            if len(h1_data) < 50:
                return MarketRegime.RANGING
            
            # Calculate regime indicators
            regime_indicators = self._calculate_regime_indicators(h1_data, h4_data, d1_data)
            
            # AI-powered regime classification
            regime_prompt = self._create_regime_analysis_prompt(regime_indicators, market_data)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": """You are an expert market regime analyst. Classify the current market regime based on the provided indicators.
                        
                        Respond with ONLY one of these regimes:
                        - TRENDING_BULL: Strong upward trend with momentum
                        - TRENDING_BEAR: Strong downward trend with momentum  
                        - RANGING: Sideways movement between support/resistance
                        - VOLATILE: High volatility without clear direction
                        - BREAKOUT: Breaking through key levels
                        - REVERSAL: Trend reversal in progress"""
                    },
                    {
                        "role": "user",
                        "content": regime_prompt
                    }
                ],
                temperature=0.1,
                max_tokens=50
            )
            
            regime_text = response.choices[0].message.content.strip().upper()
            
            # Map response to enum
            regime_mapping = {
                'TRENDING_BULL': MarketRegime.TRENDING_BULL,
                'TRENDING_BEAR': MarketRegime.TRENDING_BEAR,
                'RANGING': MarketRegime.RANGING,
                'VOLATILE': MarketRegime.VOLATILE,
                'BREAKOUT': MarketRegime.BREAKOUT,
                'REVERSAL': MarketRegime.REVERSAL
            }
            
            detected_regime = regime_mapping.get(regime_text, MarketRegime.RANGING)
            self.logger.info(f"Market regime detected: {detected_regime.value}")
            
            return detected_regime
            
        except Exception as e:
            self.logger.error(f"Error detecting market regime: {e}")
            return MarketRegime.RANGING
    
    def _calculate_regime_indicators(self, h1_data: pd.DataFrame, h4_data: pd.DataFrame, 
                                   d1_data: pd.DataFrame) -> Dict:
        """Calculate indicators for regime detection"""
        try:
            indicators = {}
            
            # Trend strength indicators
            if len(h1_data) > 0:
                h1_latest = h1_data.iloc[-1]
                
                # ADX for trend strength
                indicators['adx_h1'] = h1_latest.get('adx', 20)
                
                # EMA alignment
                ema_21 = h1_latest.get('ema_21', h1_latest.get('close', 0))
                ema_50 = h1_latest.get('ema_50', h1_latest.get('close', 0))
                current_price = h1_latest.get('close', 0)
                
                if ema_50 > 0:
                    indicators['price_vs_ema21'] = (current_price - ema_21) / ema_21
                    indicators['price_vs_ema50'] = (current_price - ema_50) / ema_50
                    indicators['ema_alignment'] = (ema_21 - ema_50) / ema_50
                
                # Volatility
                indicators['atr_h1'] = h1_latest.get('atr', 0)
                
                # RSI for momentum
                indicators['rsi_h1'] = h1_latest.get('rsi', 50)
            
            # Higher timeframe confirmation
            if len(h4_data) > 0:
                h4_latest = h4_data.iloc[-1]
                indicators['adx_h4'] = h4_latest.get('adx', 20)
                indicators['rsi_h4'] = h4_latest.get('rsi', 50)
            
            if len(d1_data) > 0:
                d1_latest = d1_data.iloc[-1]
                indicators['adx_d1'] = d1_latest.get('adx', 20)
                indicators['rsi_d1'] = d1_latest.get('rsi', 50)
            
            # Volatility regime
            if len(h1_data) >= 20:
                recent_atr = h1_data['atr'].tail(20).mean()
                historical_atr = h1_data['atr'].mean()
                indicators['volatility_ratio'] = recent_atr / historical_atr if historical_atr > 0 else 1
            
            # Range detection
            if len(h1_data) >= 50:
                recent_high = h1_data['high'].tail(50).max()
                recent_low = h1_data['low'].tail(50).min()
                current_price = h1_data['close'].iloc[-1]
                
                range_position = (current_price - recent_low) / (recent_high - recent_low) if recent_high != recent_low else 0.5
                indicators['range_position'] = range_position
                
                # Range width relative to ATR
                range_width = recent_high - recent_low
                avg_atr = h1_data['atr'].tail(20).mean()
                indicators['range_width_atr'] = range_width / avg_atr if avg_atr > 0 else 1
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating regime indicators: {e}")
            return {}
    
    def _create_regime_analysis_prompt(self, indicators: Dict, market_data: Dict) -> str:
        """Create prompt for regime analysis"""
        instrument = market_data.get('instrument', 'EUR_USD')
        current_price = market_data.get('current_price', {}).get('mid', 0)
        
        prompt = f"""
MARKET REGIME ANALYSIS FOR {instrument}

Current Price: {current_price:.5f}
Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

TREND INDICATORS:
- ADX H1: {indicators.get('adx_h1', 'N/A')} (>25 = trending)
- ADX H4: {indicators.get('adx_h4', 'N/A')}
- ADX D1: {indicators.get('adx_d1', 'N/A')}
- Price vs EMA21: {indicators.get('price_vs_ema21', 0):.4f}
- Price vs EMA50: {indicators.get('price_vs_ema50', 0):.4f}
- EMA Alignment: {indicators.get('ema_alignment', 0):.4f}

MOMENTUM INDICATORS:
- RSI H1: {indicators.get('rsi_h1', 'N/A')}
- RSI H4: {indicators.get('rsi_h4', 'N/A')}
- RSI D1: {indicators.get('rsi_d1', 'N/A')}

VOLATILITY INDICATORS:
- Current ATR: {indicators.get('atr_h1', 'N/A')}
- Volatility Ratio: {indicators.get('volatility_ratio', 'N/A')} (>1.5 = high vol)

RANGE INDICATORS:
- Range Position: {indicators.get('range_position', 'N/A')} (0=bottom, 1=top)
- Range Width/ATR: {indicators.get('range_width_atr', 'N/A')} (<3 = tight range)

Based on these indicators, classify the current market regime.
"""
        
        return prompt
    
    def _run_multi_model_analysis(self, market_data: Dict, market_regime: MarketRegime) -> Dict:
        """Run analysis using multiple AI models"""
        try:
            model_results = {}
            
            for model_name, model_config in self.models.items():
                try:
                    result = self._run_single_model_analysis(
                        market_data, market_regime, model_name, model_config
                    )
                    model_results[model_name] = result
                    
                except Exception as e:
                    self.logger.error(f"Error in {model_name} analysis: {e}")
                    model_results[model_name] = {
                        'signal': 'HOLD',
                        'confidence': 0.0,
                        'reasoning': f"Analysis failed: {str(e)}"
                    }
            
            return model_results
            
        except Exception as e:
            self.logger.error(f"Error in multi-model analysis: {e}")
            return {}
    
    def _run_single_model_analysis(self, market_data: Dict, market_regime: MarketRegime,
                                 model_name: str, model_config: Dict) -> Dict:
        """Run analysis for a single AI model"""
        try:
            # Create specialized prompt for this model
            prompt = self._create_model_specific_prompt(
                market_data, market_regime, model_name, model_config
            )
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": f"""You are a {model_config['role']} specializing in {model_config['focus']}.
                        
                        Provide your analysis in this exact JSON format:
                        {{
                            "signal": "BUY|SELL|HOLD",
                            "confidence": 0.0-1.0,
                            "reasoning": "detailed explanation",
                            "key_factors": ["factor1", "factor2", "factor3"],
                            "risk_level": "LOW|MEDIUM|HIGH",
                            "time_horizon": "SHORT|MEDIUM|LONG"
                        }}"""
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=800
            )
            
            # Parse JSON response
            response_text = response.choices[0].message.content
            
            # Extract JSON from response
            try:
                # Find JSON in the response
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}') + 1
                
                if start_idx != -1 and end_idx != -1:
                    json_str = response_text[start_idx:end_idx]
                    result = json.loads(json_str)
                else:
                    raise ValueError("No JSON found in response")
                
            except (json.JSONDecodeError, ValueError):
                # Fallback parsing
                result = self._parse_fallback_response(response_text)
            
            # Validate and normalize result
            result = self._validate_model_result(result)
            
            self.logger.debug(f"{model_name} analysis: {result['signal']} "
                            f"(confidence: {result['confidence']:.2f})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in {model_name} analysis: {e}")
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'reasoning': f"Model analysis failed: {str(e)}",
                'key_factors': [],
                'risk_level': 'HIGH',
                'time_horizon': 'MEDIUM'
            }

    def _create_model_specific_prompt(self, market_data: Dict, market_regime: MarketRegime,
                                    model_name: str, model_config: Dict) -> str:
        """Create specialized prompt for each model type"""
        instrument = market_data.get('instrument', 'EUR_USD')
        current_price = market_data.get('current_price', {}).get('mid', 0)

        base_info = f"""
MARKET ANALYSIS REQUEST - {model_config['role']}

Instrument: {instrument}
Current Price: {current_price:.5f}
Market Regime: {market_regime.value}
Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}
"""

        if model_name == 'technical_analyst':
            return self._create_technical_prompt(base_info, market_data)
        elif model_name == 'quantitative_analyst':
            return self._create_quantitative_prompt(base_info, market_data)
        elif model_name == 'macro_analyst':
            return self._create_macro_prompt(base_info, market_data)
        elif model_name == 'sentiment_analyst':
            return self._create_sentiment_prompt(base_info, market_data)
        elif model_name == 'risk_manager':
            return self._create_risk_prompt(base_info, market_data)
        else:
            return base_info + "\nProvide your trading recommendation."

    def _create_technical_prompt(self, base_info: str, market_data: Dict) -> str:
        """Create technical analysis prompt"""
        h1_data = market_data.get('H1', pd.DataFrame())
        h4_data = market_data.get('H4', pd.DataFrame())

        if len(h1_data) == 0:
            return base_info + "\nInsufficient data for technical analysis."

        h1_latest = h1_data.iloc[-1]
        h4_latest = h4_data.iloc[-1] if len(h4_data) > 0 else h1_latest

        technical_data = f"""
TECHNICAL INDICATORS:

H1 Timeframe:
- RSI: {h1_latest.get('rsi', 'N/A')}
- MACD: {h1_latest.get('macd', 'N/A')}
- MACD Signal: {h1_latest.get('macd_signal', 'N/A')}
- ADX: {h1_latest.get('adx', 'N/A')}
- ATR: {h1_latest.get('atr', 'N/A')}
- EMA 21: {h1_latest.get('ema_21', 'N/A')}
- EMA 50: {h1_latest.get('ema_50', 'N/A')}
- Bollinger Upper: {h1_latest.get('bb_upper', 'N/A')}
- Bollinger Lower: {h1_latest.get('bb_lower', 'N/A')}
- Stochastic K: {h1_latest.get('stoch_k', 'N/A')}
- Stochastic D: {h1_latest.get('stoch_d', 'N/A')}

H4 Timeframe:
- RSI: {h4_latest.get('rsi', 'N/A')}
- MACD: {h4_latest.get('macd', 'N/A')}
- ADX: {h4_latest.get('adx', 'N/A')}
- EMA 21: {h4_latest.get('ema_21', 'N/A')}
- EMA 50: {h4_latest.get('ema_50', 'N/A')}

SUPPORT/RESISTANCE LEVELS:
- Recent High (50 periods): {h1_data['high'].tail(50).max():.5f}
- Recent Low (50 periods): {h1_data['low'].tail(50).min():.5f}
- VWAP: {h1_latest.get('vwap', 'N/A')}
- Parabolic SAR: {h1_latest.get('parabolic_sar', 'N/A')}

Focus on chart patterns, indicator divergences, support/resistance breaks, and multi-timeframe confirmation.
"""

        return base_info + technical_data

    def _analyze_patterns(self, market_data: Dict) -> Dict:
        """Analyze chart patterns and technical formations"""
        try:
            h1_data = market_data.get('H1', pd.DataFrame())

            if len(h1_data) < 50:
                return {'patterns': [], 'strength': 0.0}

            patterns = []

            # Simple pattern detection
            recent_highs = h1_data['high'].tail(20)
            recent_lows = h1_data['low'].tail(20)

            # Double top/bottom detection
            if self._detect_double_top(recent_highs):
                patterns.append({'type': 'double_top', 'strength': 0.7, 'signal': 'bearish'})

            if self._detect_double_bottom(recent_lows):
                patterns.append({'type': 'double_bottom', 'strength': 0.7, 'signal': 'bullish'})

            # Trend line breaks
            if self._detect_trendline_break(h1_data):
                patterns.append({'type': 'trendline_break', 'strength': 0.6, 'signal': 'reversal'})

            # Calculate overall pattern strength
            if patterns:
                avg_strength = np.mean([p['strength'] for p in patterns])
            else:
                avg_strength = 0.0

            return {
                'patterns': patterns,
                'strength': avg_strength,
                'count': len(patterns)
            }

        except Exception as e:
            self.logger.error(f"Error analyzing patterns: {e}")
            return {'patterns': [], 'strength': 0.0}

    def _detect_double_top(self, highs: pd.Series) -> bool:
        """Simple double top detection"""
        try:
            if len(highs) < 10:
                return False

            max_high = highs.max()
            high_indices = highs[highs > max_high * 0.995].index

            return len(high_indices) >= 2 and (high_indices[-1] - high_indices[0]) > 5

        except Exception:
            return False

    def _detect_double_bottom(self, lows: pd.Series) -> bool:
        """Simple double bottom detection"""
        try:
            if len(lows) < 10:
                return False

            min_low = lows.min()
            low_indices = lows[lows < min_low * 1.005].index

            return len(low_indices) >= 2 and (low_indices[-1] - low_indices[0]) > 5

        except Exception:
            return False

    def _detect_trendline_break(self, data: pd.DataFrame) -> bool:
        """Simple trendline break detection"""
        try:
            if len(data) < 20:
                return False

            # Simple trend detection using linear regression
            recent_closes = data['close'].tail(20).values
            x = np.arange(len(recent_closes))

            # Calculate trend
            slope = np.polyfit(x, recent_closes, 1)[0]

            # Check for recent break
            current_price = recent_closes[-1]
            trend_price = recent_closes[0] + slope * (len(recent_closes) - 1)

            # If price deviates significantly from trend
            deviation = abs(current_price - trend_price) / trend_price

            return deviation > 0.01  # 1% deviation

        except Exception:
            return False

    def _analyze_market_sentiment(self, market_data: Dict, instruments: List[str] = None) -> Dict:
        """Analyze market sentiment using helper methods"""
        from .ai_analyzer_methods import AIAnalyzerMethods
        helper = AIAnalyzerMethods(self)
        return helper.analyze_market_sentiment(market_data, instruments)

    def _assess_risk(self, market_data: Dict, market_regime: MarketRegime) -> Dict:
        """Assess risk using helper methods"""
        from .ai_analyzer_methods import AIAnalyzerMethods
        helper = AIAnalyzerMethods(self)
        return helper.assess_risk(market_data, market_regime)

    def _select_strategies(self, market_regime: MarketRegime, model_results: Dict, patterns: Dict) -> List[str]:
        """Select strategies using helper methods"""
        from .ai_analyzer_methods import AIAnalyzerMethods
        helper = AIAnalyzerMethods(self)
        return helper.select_strategies(market_regime, model_results, patterns)

    def _forecast_probabilities(self, market_data: Dict, model_results: Dict) -> Dict:
        """Forecast probabilities using helper methods"""
        from .ai_analyzer_methods import AIAnalyzerMethods
        helper = AIAnalyzerMethods(self)
        return helper.forecast_probabilities(market_data, model_results)

    def _ensemble_decision(self, model_results: Dict, patterns: Dict, sentiment: Dict,
                          risk_assessment: Dict, strategy_recommendations: List[str],
                          probability_dist: Dict, market_regime: MarketRegime) -> Dict:
        """Make ensemble decision using helper methods"""
        from .ai_analyzer_methods import AIAnalyzerMethods
        helper = AIAnalyzerMethods(self)
        return helper.ensemble_decision(
            model_results, patterns, sentiment, risk_assessment,
            strategy_recommendations, probability_dist, market_regime
        )

    def _create_fallback_result(self, market_data: Dict) -> AIAnalysisResult:
        """Create fallback result using helper methods"""
        from .ai_analyzer_methods import AIAnalyzerMethods
        helper = AIAnalyzerMethods(self)
        return helper.create_fallback_result(market_data)

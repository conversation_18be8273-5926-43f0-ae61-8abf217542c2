"""
Additional methods for Advanced AI Analyzer
Contains the remaining analysis methods to complete the AI engine
"""

import numpy as np
import pandas as pd
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from .advanced_ai_analyzer import MarketRegime, AIAnalysisResult

class AIAnalyzerMethods:
    """Additional methods for the AI analyzer"""
    
    def __init__(self, analyzer):
        self.analyzer = analyzer
        self.logger = logging.getLogger(__name__)
    
    def analyze_market_sentiment(self, market_data: Dict, instruments: List[str] = None) -> Dict:
        """Analyze overall market sentiment"""
        try:
            h1_data = market_data.get('H1', pd.DataFrame())
            
            if len(h1_data) < 20:
                return {'score': 0.5, 'direction': 'neutral', 'strength': 'weak'}
            
            # Volume-based sentiment
            recent_volume = h1_data['volume'].tail(10).mean()
            historical_volume = h1_data['volume'].mean()
            volume_sentiment = min(recent_volume / historical_volume, 2.0) / 2.0
            
            # Price momentum sentiment
            price_change = (h1_data['close'].iloc[-1] / h1_data['close'].iloc[-20] - 1)
            momentum_sentiment = 0.5 + (price_change * 10)  # Scale to 0-1
            momentum_sentiment = max(0, min(1, momentum_sentiment))
            
            # Volatility sentiment (high vol = fear)
            current_atr = h1_data['atr'].iloc[-1] if 'atr' in h1_data.columns else 0
            avg_atr = h1_data['atr'].mean() if 'atr' in h1_data.columns else current_atr
            volatility_ratio = current_atr / avg_atr if avg_atr > 0 else 1
            volatility_sentiment = max(0, min(1, 1 - (volatility_ratio - 1) * 0.5))
            
            # Combine sentiments
            overall_sentiment = (volume_sentiment * 0.3 + momentum_sentiment * 0.5 + 
                               volatility_sentiment * 0.2)
            
            # Classify sentiment
            if overall_sentiment > 0.7:
                direction = 'bullish'
                strength = 'strong'
            elif overall_sentiment > 0.6:
                direction = 'bullish'
                strength = 'moderate'
            elif overall_sentiment < 0.3:
                direction = 'bearish'
                strength = 'strong'
            elif overall_sentiment < 0.4:
                direction = 'bearish'
                strength = 'moderate'
            else:
                direction = 'neutral'
                strength = 'weak'
            
            return {
                'score': overall_sentiment,
                'direction': direction,
                'strength': strength,
                'components': {
                    'volume': volume_sentiment,
                    'momentum': momentum_sentiment,
                    'volatility': volatility_sentiment
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing sentiment: {e}")
            return {'score': 0.5, 'direction': 'neutral', 'strength': 'weak'}
    
    def assess_risk(self, market_data: Dict, market_regime: MarketRegime) -> Dict:
        """Comprehensive risk assessment"""
        try:
            h1_data = market_data.get('H1', pd.DataFrame())
            current_price = market_data.get('current_price', {}).get('mid', 0)
            
            if len(h1_data) < 50:
                return {'overall_risk': 'HIGH', 'risk_score': 0.8}
            
            # Volatility risk
            returns = h1_data['close'].pct_change().dropna()
            volatility = returns.std()
            volatility_risk = min(volatility * 100, 1.0)  # Scale to 0-1
            
            # Liquidity risk (based on volume)
            volume_consistency = 1 - (h1_data['volume'].std() / h1_data['volume'].mean())
            liquidity_risk = max(0, 1 - volume_consistency)
            
            # Trend risk (trending markets are generally less risky)
            adx = h1_data['adx'].iloc[-1] if 'adx' in h1_data.columns else 20
            trend_risk = max(0, (30 - adx) / 30)  # Higher ADX = lower trend risk
            
            # Market regime risk
            regime_risk_map = {
                MarketRegime.TRENDING_BULL: 0.3,
                MarketRegime.TRENDING_BEAR: 0.3,
                MarketRegime.RANGING: 0.5,
                MarketRegime.VOLATILE: 0.8,
                MarketRegime.BREAKOUT: 0.6,
                MarketRegime.REVERSAL: 0.7
            }
            regime_risk = regime_risk_map.get(market_regime, 0.5)
            
            # Time-based risk (avoid major news times)
            current_hour = datetime.now().hour
            time_risk = 0.8 if current_hour in [8, 14, 16] else 0.3  # Major news hours
            
            # Overall risk calculation
            risk_components = {
                'volatility': volatility_risk,
                'liquidity': liquidity_risk,
                'trend': trend_risk,
                'regime': regime_risk,
                'time': time_risk
            }
            
            # Weighted risk score
            weights = {'volatility': 0.3, 'liquidity': 0.2, 'trend': 0.2, 'regime': 0.2, 'time': 0.1}
            overall_risk_score = sum(risk_components[k] * weights[k] for k in weights.keys())
            
            # Risk classification
            if overall_risk_score > 0.7:
                risk_level = 'HIGH'
            elif overall_risk_score > 0.5:
                risk_level = 'MEDIUM'
            else:
                risk_level = 'LOW'
            
            # Volatility forecast
            volatility_forecast = min(volatility * 1.2, 1.0)  # Slightly higher than current
            
            return {
                'overall_risk': risk_level,
                'risk_score': overall_risk_score,
                'volatility_forecast': volatility_forecast,
                'components': risk_components,
                'recommendations': self._get_risk_recommendations(risk_level, risk_components)
            }
            
        except Exception as e:
            self.logger.error(f"Error assessing risk: {e}")
            return {'overall_risk': 'HIGH', 'risk_score': 0.8}
    
    def select_strategies(self, market_regime: MarketRegime, model_results: Dict, 
                         patterns: Dict) -> List[str]:
        """Select appropriate trading strategies based on market conditions"""
        try:
            strategies = []
            
            # Strategy selection based on market regime
            if market_regime == MarketRegime.TRENDING_BULL:
                strategies.extend(['TrendFollowing', 'DayTrading'])
            elif market_regime == MarketRegime.TRENDING_BEAR:
                strategies.extend(['TrendFollowing', 'DayTrading'])
            elif market_regime == MarketRegime.RANGING:
                strategies.extend(['MeanReversion', 'Scalping'])
            elif market_regime == MarketRegime.VOLATILE:
                strategies.extend(['Scalping'])
            elif market_regime == MarketRegime.BREAKOUT:
                strategies.extend(['BreakoutTrading', 'DayTrading'])
            elif market_regime == MarketRegime.REVERSAL:
                strategies.extend(['ReversalTrading', 'DayTrading'])
            
            # Add strategies based on model consensus
            bullish_models = sum(1 for result in model_results.values() 
                               if result.get('signal') == 'BUY')
            bearish_models = sum(1 for result in model_results.values() 
                               if result.get('signal') == 'SELL')
            
            if bullish_models >= 3:
                if 'TrendFollowing' not in strategies:
                    strategies.append('TrendFollowing')
            elif bearish_models >= 3:
                if 'TrendFollowing' not in strategies:
                    strategies.append('TrendFollowing')
            
            # Add strategies based on patterns
            if patterns.get('strength', 0) > 0.6:
                strategies.append('PatternTrading')
            
            # Ensure we have at least one strategy
            if not strategies:
                strategies = ['DayTrading']
            
            return strategies[:3]  # Limit to top 3 strategies
            
        except Exception as e:
            self.logger.error(f"Error selecting strategies: {e}")
            return ['DayTrading']
    
    def forecast_probabilities(self, market_data: Dict, model_results: Dict) -> Dict:
        """Forecast probability distribution of outcomes"""
        try:
            # Count model signals
            buy_signals = sum(1 for result in model_results.values() 
                            if result.get('signal') == 'BUY')
            sell_signals = sum(1 for result in model_results.values() 
                             if result.get('signal') == 'SELL')
            hold_signals = sum(1 for result in model_results.values() 
                             if result.get('signal') == 'HOLD')
            
            total_models = len(model_results)
            
            if total_models == 0:
                return {'buy': 0.33, 'sell': 0.33, 'hold': 0.34}
            
            # Calculate base probabilities
            buy_prob = buy_signals / total_models
            sell_prob = sell_signals / total_models
            hold_prob = hold_signals / total_models
            
            # Adjust based on confidence levels
            avg_confidence = np.mean([result.get('confidence', 0.5) 
                                    for result in model_results.values()])
            
            # Higher confidence increases the probability of the dominant signal
            if buy_prob > sell_prob and buy_prob > hold_prob:
                buy_prob = min(buy_prob * (1 + avg_confidence * 0.5), 0.9)
                remaining = 1 - buy_prob
                sell_prob = sell_prob * remaining / (sell_prob + hold_prob) if (sell_prob + hold_prob) > 0 else remaining / 2
                hold_prob = remaining - sell_prob
            elif sell_prob > buy_prob and sell_prob > hold_prob:
                sell_prob = min(sell_prob * (1 + avg_confidence * 0.5), 0.9)
                remaining = 1 - sell_prob
                buy_prob = buy_prob * remaining / (buy_prob + hold_prob) if (buy_prob + hold_prob) > 0 else remaining / 2
                hold_prob = remaining - buy_prob
            else:
                hold_prob = min(hold_prob * (1 + avg_confidence * 0.3), 0.8)
                remaining = 1 - hold_prob
                buy_prob = buy_prob * remaining / (buy_prob + sell_prob) if (buy_prob + sell_prob) > 0 else remaining / 2
                sell_prob = remaining - buy_prob
            
            # Ensure probabilities sum to 1
            total = buy_prob + sell_prob + hold_prob
            if total > 0:
                buy_prob /= total
                sell_prob /= total
                hold_prob /= total
            
            return {
                'buy': round(buy_prob, 3),
                'sell': round(sell_prob, 3),
                'hold': round(hold_prob, 3),
                'confidence': avg_confidence
            }
            
        except Exception as e:
            self.logger.error(f"Error forecasting probabilities: {e}")
            return {'buy': 0.33, 'sell': 0.33, 'hold': 0.34, 'confidence': 0.5}
    
    def ensemble_decision(self, model_results: Dict, patterns: Dict, sentiment: Dict,
                         risk_assessment: Dict, strategy_recommendations: List[str],
                         probability_dist: Dict, market_regime: MarketRegime) -> Dict:
        """Make final ensemble decision combining all analyses"""
        try:
            # Weight different components
            weights = {
                'models': 0.5,
                'patterns': 0.2,
                'sentiment': 0.15,
                'risk': 0.1,
                'regime': 0.05
            }
            
            # Calculate weighted scores
            buy_score = 0
            sell_score = 0
            
            # Model results
            for model_name, result in model_results.items():
                model_weight = self.analyzer.models[model_name]['weight']
                confidence = result.get('confidence', 0.5)
                
                if result.get('signal') == 'BUY':
                    buy_score += weights['models'] * model_weight * confidence
                elif result.get('signal') == 'SELL':
                    sell_score += weights['models'] * model_weight * confidence
            
            # Pattern analysis
            pattern_strength = patterns.get('strength', 0)
            pattern_signals = [p for p in patterns.get('patterns', []) if p.get('signal') in ['bullish', 'bearish']]
            
            for pattern in pattern_signals:
                if pattern['signal'] == 'bullish':
                    buy_score += weights['patterns'] * pattern['strength']
                elif pattern['signal'] == 'bearish':
                    sell_score += weights['patterns'] * pattern['strength']
            
            # Sentiment analysis
            sentiment_score = sentiment.get('score', 0.5)
            if sentiment_score > 0.6:
                buy_score += weights['sentiment'] * (sentiment_score - 0.5) * 2
            elif sentiment_score < 0.4:
                sell_score += weights['sentiment'] * (0.5 - sentiment_score) * 2
            
            # Risk adjustment (high risk reduces confidence)
            risk_score = risk_assessment.get('risk_score', 0.5)
            risk_adjustment = max(0.5, 1 - risk_score)
            buy_score *= risk_adjustment
            sell_score *= risk_adjustment
            
            # Market regime adjustment
            regime_bullish = market_regime in [MarketRegime.TRENDING_BULL, MarketRegime.BREAKOUT]
            regime_bearish = market_regime in [MarketRegime.TRENDING_BEAR]
            
            if regime_bullish:
                buy_score += weights['regime']
            elif regime_bearish:
                sell_score += weights['regime']
            
            # Determine final signal
            if buy_score > sell_score and buy_score > 0.6:
                signal = 'BUY'
                confidence = min(buy_score, 1.0)
            elif sell_score > buy_score and sell_score > 0.6:
                signal = 'SELL'
                confidence = min(sell_score, 1.0)
            else:
                signal = 'HOLD'
                confidence = max(buy_score, sell_score)
            
            # Generate reasoning
            reasoning = self._generate_ensemble_reasoning(
                signal, confidence, model_results, patterns, sentiment, 
                risk_assessment, market_regime
            )
            
            # Calculate entry and exit zones
            current_price = 0  # This would be passed from market_data
            entry_zones, exit_zones = self._calculate_entry_exit_zones(
                signal, current_price, risk_assessment
            )
            
            return {
                'signal': signal,
                'confidence': confidence,
                'reasoning': reasoning,
                'entry_zones': entry_zones,
                'exit_zones': exit_zones,
                'buy_score': buy_score,
                'sell_score': sell_score
            }
            
        except Exception as e:
            self.logger.error(f"Error in ensemble decision: {e}")
            return {
                'signal': 'HOLD',
                'confidence': 0.0,
                'reasoning': f"Ensemble decision failed: {str(e)}",
                'entry_zones': [],
                'exit_zones': []
            }
    
    def _get_risk_recommendations(self, risk_level: str, risk_components: Dict) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []
        
        if risk_level == 'HIGH':
            recommendations.extend([
                "Reduce position size significantly",
                "Use tight stop losses",
                "Consider avoiding trading during this period",
                "Monitor positions closely"
            ])
        elif risk_level == 'MEDIUM':
            recommendations.extend([
                "Use moderate position sizing",
                "Implement proper stop losses",
                "Monitor market conditions"
            ])
        else:
            recommendations.extend([
                "Normal position sizing acceptable",
                "Standard risk management applies"
            ])
        
        # Specific recommendations based on risk components
        if risk_components.get('volatility', 0) > 0.7:
            recommendations.append("High volatility detected - use wider stops")
        
        if risk_components.get('liquidity', 0) > 0.6:
            recommendations.append("Low liquidity - expect higher slippage")
        
        return recommendations
    
    def _generate_ensemble_reasoning(self, signal: str, confidence: float, 
                                   model_results: Dict, patterns: Dict, sentiment: Dict,
                                   risk_assessment: Dict, market_regime: MarketRegime) -> str:
        """Generate comprehensive reasoning for the ensemble decision"""
        
        # Count model agreements
        model_signals = [result.get('signal', 'HOLD') for result in model_results.values()]
        signal_count = model_signals.count(signal)
        
        reasoning_parts = [
            f"Ensemble Analysis: {signal} signal with {confidence:.2f} confidence",
            f"Model Consensus: {signal_count}/{len(model_results)} models agree",
            f"Market Regime: {market_regime.value}",
            f"Risk Level: {risk_assessment.get('overall_risk', 'MEDIUM')}",
            f"Sentiment: {sentiment.get('direction', 'neutral')} ({sentiment.get('strength', 'weak')})"
        ]
        
        if patterns.get('patterns'):
            pattern_types = [p['type'] for p in patterns['patterns']]
            reasoning_parts.append(f"Patterns detected: {', '.join(pattern_types)}")
        
        return ". ".join(reasoning_parts)
    
    def _calculate_entry_exit_zones(self, signal: str, current_price: float, 
                                  risk_assessment: Dict) -> Tuple[List[float], List[float]]:
        """Calculate optimal entry and exit zones"""
        if current_price <= 0:
            return [], []
        
        atr_estimate = current_price * 0.001  # Rough ATR estimate
        volatility = risk_assessment.get('volatility_forecast', 0.5)
        
        # Adjust zones based on volatility
        zone_width = atr_estimate * (1 + volatility)
        
        if signal == 'BUY':
            entry_zones = [
                current_price - zone_width * 0.5,
                current_price,
                current_price + zone_width * 0.3
            ]
            exit_zones = [
                current_price + zone_width * 2,
                current_price + zone_width * 3,
                current_price + zone_width * 4
            ]
        elif signal == 'SELL':
            entry_zones = [
                current_price + zone_width * 0.5,
                current_price,
                current_price - zone_width * 0.3
            ]
            exit_zones = [
                current_price - zone_width * 2,
                current_price - zone_width * 3,
                current_price - zone_width * 4
            ]
        else:
            entry_zones = [current_price]
            exit_zones = [current_price]
        
        return entry_zones, exit_zones
    
    def create_fallback_result(self, market_data: Dict) -> AIAnalysisResult:
        """Create fallback result when analysis fails"""
        return AIAnalysisResult(
            primary_signal='HOLD',
            confidence=0.0,
            market_regime=MarketRegime.RANGING,
            strategy_recommendations=['DayTrading'],
            risk_assessment={'overall_risk': 'HIGH', 'risk_score': 0.8},
            entry_zones=[],
            exit_zones=[],
            reasoning="Analysis failed - insufficient data or system error",
            technical_summary={'patterns': [], 'strength': 0.0},
            sentiment_score=0.5,
            volatility_forecast=0.5,
            probability_distribution={'buy': 0.33, 'sell': 0.33, 'hold': 0.34},
            metadata={
                'analysis_timestamp': datetime.now().isoformat(),
                'error': True,
                'instrument': market_data.get('instrument', 'UNKNOWN')
            }
        )

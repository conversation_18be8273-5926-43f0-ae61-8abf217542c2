# OANDA Setup Guide

## Step 1: Create OANDA Practice Account

1. **Go to OANDA website**: https://www.oanda.com/
2. **Click "Open Account"** or "Try Demo"
3. **Choose "Practice Account"** (this is FREE and uses virtual money)
4. **Fill out the registration form**:
   - Email address
   - Password
   - Country
   - Phone number (optional)
5. **Verify your email** by clicking the link sent to your inbox
6. **Complete account setup**

## Step 2: Access Your Account Dashboard

1. **Log in** to your OANDA account
2. **Go to "My Account"** section
3. **Find your Account ID** - it looks like: `101-**********-001`
4. **Write down your Account ID** - you'll need this for the bot

## Step 3: Generate API Token

1. **In your OANDA dashboard**, look for:
   - "Manage API Access" or
   - "API" section or 
   - "Developer" section
2. **Click "Generate Token"** or "Create API Key"
3. **Give it a name** like "Forex Trading Bot"
4. **Copy the API token** - it looks like: `abc123def456ghi789...`
5. **⚠️ IMPORTANT**: Save this token immediately - you can't see it again!

## Step 4: Verify Your Setup

Your practice account comes with:
- **$100,000 virtual money** (not real money)
- **Access to all currency pairs**
- **Real-time market data**
- **Full trading capabilities**

## Step 5: Test Connection

Run this command to test your OANDA connection:

```bash
python test_oanda_connection.py
```

## Important Notes

### Practice vs Live Account
- **Practice Account**: Uses virtual money, perfect for testing
- **Live Account**: Uses real money, only use after thorough testing

### API Limits
- **Practice Account**: 120 requests per minute
- **Live Account**: Higher limits available

### Supported Currency Pairs
- EUR_USD (Euro/US Dollar)
- GBP_USD (British Pound/US Dollar)  
- USD_JPY (US Dollar/Japanese Yen)
- AUD_USD (Australian Dollar/US Dollar)
- USD_CAD (US Dollar/Canadian Dollar)
- And many more...

## Troubleshooting

### Can't find API section?
- Try logging out and back in
- Look for "Developer Tools" or "API Access"
- Contact OANDA support if needed

### API token not working?
- Make sure you copied the entire token
- Check that your account is verified
- Ensure you're using the practice environment

### Account ID format
- Should look like: `101-**********-001`
- Include all dashes and numbers
- Don't include any spaces

## Security Tips

1. **Never share your API token**
2. **Use practice account for testing**
3. **Keep your login credentials secure**
4. **Monitor your account regularly**

## Next Steps

After completing OANDA setup:
1. Add your API key and Account ID to `config.env`
2. Run the connection test
3. Start the trading bot in demo mode

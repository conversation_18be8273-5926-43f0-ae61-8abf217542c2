#!/usr/bin/env python3
"""
Comprehensive Strategy Testing and Comparison Demo
Demonstrates the enhanced trading robot with multiple strategy testing and ensemble methods
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import time
import threading

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.strategies.strategy_tester import ComprehensiveStrategyTester
from src.strategies.scalping_strategy import ScalpingStrategy
from src.strategies.day_trading_strategy import DayTradingStrategy
from src.strategies.trend_following_strategy import TrendFollowingStrategy
from src.cli.beautiful_cli import BeautifulCLI
from src.market_data.data_provider import AdvancedDataProvider, MarketDataConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('strategy_testing.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def generate_sample_data(days: int = 365) -> Dict[str, pd.DataFrame]:
    """Generate sample market data for testing"""
    logger.info(f"Generating {days} days of sample market data...")
    
    # Generate realistic forex data
    np.random.seed(42)  # For reproducible results
    
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), 
                         end=datetime.now(), freq='1H')
    
    # EUR/USD sample data
    initial_price = 1.0850
    returns = np.random.normal(0, 0.001, len(dates))  # Small hourly returns
    prices = [initial_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    # Create OHLC data
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        # Generate realistic OHLC from price
        spread = np.random.uniform(0.0001, 0.0003)
        volatility = np.random.uniform(0.0005, 0.002)
        
        high = price + volatility
        low = price - volatility
        open_price = prices[i-1] if i > 0 else price
        close = price
        
        # Volume (random but realistic)
        volume = np.random.randint(1000, 10000)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume,
            'spread': spread
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    # Add technical indicators
    df = add_technical_indicators(df)
    
    # Create data for different timeframes
    market_data = {
        'M1': df.resample('1T').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna(),
        'M5': df.resample('5T').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna(),
        'M15': df.resample('15T').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna(),
        'H1': df.resample('1H').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna(),
        'H4': df.resample('4H').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna(),
        'D1': df.resample('1D').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()
    }
    
    # Add technical indicators to each timeframe
    for timeframe, data in market_data.items():
        market_data[timeframe] = add_technical_indicators(data)
    
    logger.info("Sample data generation completed")
    return market_data

def add_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """Add technical indicators to dataframe"""
    try:
        # Moving averages
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_21'] = df['close'].ewm(span=21).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        df['ema_50'] = df['close'].ewm(span=50).mean()
        df['ema_100'] = df['close'].ewm(span=100).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # ATR
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)
        df['atr'] = true_range.rolling(window=14).mean()
        
        # ADX (simplified)
        df['adx'] = 25 + np.random.normal(0, 5, len(df))  # Simplified for demo
        df['adx'] = df['adx'].clip(0, 100)
        
        # VWAP
        df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
        
        return df
        
    except Exception as e:
        logger.error(f"Error adding technical indicators: {e}")
        return df

def run_cli_demo(tester: ComprehensiveStrategyTester):
    """Run CLI demo with live updates"""
    logger.info("Starting CLI demo...")
    
    # Initialize CLI
    cli = BeautifulCLI()
    
    # Start CLI in separate thread
    cli_thread = threading.Thread(target=cli.start, daemon=True)
    cli_thread.start()
    
    # Simulate live updates
    try:
        for i in range(60):  # Run for 60 seconds
            # Update CLI with test results
            cli_data = tester.get_cli_data()
            
            if cli_data:
                cli.update_strategy_metrics(cli_data.get('strategy_metrics', {}))
                cli.update_ensemble_results(cli_data.get('ensemble_results', {}))
                cli.update_strategy_rankings(cli_data.get('rankings', []))
                
                # Update market data (simulated)
                cli.update_market_data({
                    'EUR_USD': {
                        'current_price': {'mid': 1.0850 + np.random.normal(0, 0.001)},
                        'spread': np.random.uniform(1.0, 2.0)
                    }
                })
                
                # Add sample signals
                if i % 10 == 0:  # Every 10 seconds
                    signal_types = ['BUY', 'SELL', 'HOLD']
                    cli.add_signal({
                        'timestamp': datetime.now(),
                        'signal_type': np.random.choice(signal_types),
                        'confidence': np.random.uniform(0.6, 0.95),
                        'instrument': 'EUR_USD',
                        'strategy': 'Ensemble'
                    })
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("CLI demo interrupted by user")
    finally:
        cli.stop()

def main():
    """Main function to run comprehensive strategy testing"""
    logger.info("Starting Comprehensive Strategy Testing Demo")
    
    try:
        # Generate sample market data
        market_data = generate_sample_data(days=180)  # 6 months of data
        
        # Initialize comprehensive tester
        tester = ComprehensiveStrategyTester(initial_capital=100000)
        
        # Define test period
        end_date = datetime.now()
        start_date = end_date - timedelta(days=120)  # 4 months for testing
        
        # Create strategies to test
        strategies = [
            ScalpingStrategy(),
            DayTradingStrategy(), 
            TrendFollowingStrategy()
        ]
        
        logger.info("Running comprehensive strategy test...")
        
        # Run comprehensive test
        results = tester.run_comprehensive_test(
            market_data=market_data,
            strategies=strategies,
            start_date=start_date,
            end_date=end_date,
            optimize_parameters=True,
            test_ensembles=True
        )
        
        # Display results summary
        print("\n" + "="*80)
        print("COMPREHENSIVE STRATEGY TESTING RESULTS")
        print("="*80)
        
        # Individual strategy results
        print("\n📊 INDIVIDUAL STRATEGY PERFORMANCE:")
        print("-" * 50)
        individual_results = results.get('individual_results', {})
        
        for strategy_name, strategy_data in individual_results.items():
            metrics = strategy_data.get('basic_metrics', {})
            print(f"\n{strategy_name}:")
            print(f"  Total Return: {metrics.get('total_return', 0):.2f}%")
            print(f"  Win Rate: {metrics.get('win_rate', 0):.1%}")
            print(f"  Sharpe Ratio: {metrics.get('sharpe_ratio', 0):.2f}")
            print(f"  Max Drawdown: {metrics.get('max_drawdown', 0):.1f}%")
            print(f"  Total Trades: {metrics.get('total_trades', 0)}")
        
        # Ensemble results
        print("\n🤖 ENSEMBLE METHOD PERFORMANCE:")
        print("-" * 50)
        ensemble_results = results.get('ensemble_results', {})
        
        for ensemble_name, ensemble_data in ensemble_results.items():
            performance = ensemble_data.get('performance', {})
            print(f"\n{ensemble_data.get('method', ensemble_name)}:")
            print(f"  Total Return: {performance.get('total_return', 0):.2f}%")
            print(f"  Win Rate: {performance.get('win_rate', 0):.1%}")
            print(f"  Sharpe Ratio: {performance.get('sharpe_ratio', 0):.2f}")
            print(f"  Max Drawdown: {performance.get('max_drawdown', 0):.1f}%")
        
        # Optimization results
        optimization_results = results.get('optimization_results', {})
        if optimization_results:
            print("\n⚙️  PARAMETER OPTIMIZATION RESULTS:")
            print("-" * 50)
            
            for strategy_name, opt_data in optimization_results.items():
                if 'error' not in opt_data:
                    improvements = opt_data.get('improvement_metrics', {})
                    print(f"\n{strategy_name}:")
                    print(f"  Sharpe Improvement: {improvements.get('sharpe_ratio_improvement_pct', 0):.1f}%")
                    print(f"  Return Improvement: {improvements.get('total_pnl_improvement_pct', 0):.1f}%")
                    print(f"  Drawdown Improvement: {improvements.get('max_drawdown_improvement_pct', 0):.1f}%")
        
        # Best performers
        summary = results.get('summary', {})
        print("\n🏆 BEST PERFORMERS:")
        print("-" * 50)
        
        best_individual = summary.get('best_individual_strategy', {})
        if best_individual:
            print(f"Best Individual: {best_individual.get('name', 'N/A')} "
                  f"(Sharpe: {best_individual.get('sharpe_ratio', 0):.2f})")
        
        best_ensemble = summary.get('best_ensemble_method', {})
        if best_ensemble:
            print(f"Best Ensemble: {best_ensemble.get('name', 'N/A')} "
                  f"(Sharpe: {best_ensemble.get('sharpe_ratio', 0):.2f})")
        
        # Recommendations
        recommendations = results.get('recommendations', {})
        if recommendations:
            print("\n💡 RECOMMENDATIONS:")
            print("-" * 50)
            
            best_practices = recommendations.get('best_practices', {})
            if best_practices:
                top_performers = best_practices.get('top_performers', [])
                print(f"Top Performers: {', '.join(top_performers)}")
                
                characteristics = best_practices.get('common_characteristics', [])
                for char in characteristics:
                    print(f"  • {char}")
        
        # Save results
        results_file = f"strategy_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        tester.save_results(results_file)
        print(f"\n💾 Results saved to: {results_file}")
        
        # Ask user if they want to see CLI demo
        print("\n" + "="*80)
        response = input("Would you like to see the CLI demo? (y/n): ").lower().strip()
        
        if response in ['y', 'yes']:
            print("\nStarting CLI demo... Press Ctrl+C to exit")
            run_cli_demo(tester)
        
        print("\n✅ Comprehensive strategy testing completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Testing interrupted by user")
        print("\n⚠️  Testing interrupted by user")
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        print(f"\n❌ Error: {e}")
    finally:
        print("\nThank you for using the Comprehensive Strategy Testing System!")

if __name__ == "__main__":
    main()
